// 渐变色数据
// 从FY.csv提取，共167条数据
const GRADIENT_DATA = [
  {
    "id": "1",
    "name": "Above_The_Sky",
    "chineseName": "天空之上",
    "cssStyle": "linear-gradient(to top, lightgrey 0%, lightgrey 1%, #e0e0e0 26%, #efefef 48%, #d9d9d9 75%, #bcbcbc 100%)"
  },
  {
    "id": "2",
    "name": "African_Field",
    "chineseName": "非洲田野",
    "cssStyle": "linear-gradient(to top, #65bd60 0%, #5ac1a8 25%, #3ec6ed 50%, #b7ddb7 75%, #fef381 100%)"
  },
  {
    "id": "3",
    "name": "Alchemist_Lab",
    "chineseName": "炼金术实验室",
    "cssStyle": "linear-gradient(-20deg, #d558c8 0%, #24d292 100%)"
  },
  {
    "id": "4",
    "name": "<PERSON>our_Amour",
    "chineseName": "爱之爱",
    "cssStyle": "linear-gradient(to top, #f77062 0%, #fe5196 100%)"
  },
  {
    "id": "5",
    "name": "Amy_Crisp",
    "chineseName": "艾米脆饼",
    "cssStyle": "linear-gradient(120deg, #a6c0fe 0%, #f68084 100%)"
  },
  {
    "id": "6",
    "name": "Angel_Care",
    "chineseName": "天使关怀",
    "cssStyle": "linear-gradient(-225deg, #FFE29F 0%, #FFA99F 48%, #FF719A 100%)"
  },
  {
    "id": "7",
    "name": "Aqua_Guidance",
    "chineseName": "水之引导",
    "cssStyle": "linear-gradient(to top, #007adf 0%, #00ecbc 100%)"
  },
  {
    "id": "8",
    "name": "Aqua_Splash",
    "chineseName": "水花飞溅",
    "cssStyle": "linear-gradient(15deg, #13547a 0%, #80d0c7 100%)"
  },
  {
    "id": "9",
    "name": "Arielles_Smile",
    "chineseName": "艾瑞尔的微笑",
    "cssStyle": "radial-gradient(circle 248px at center, #16d9e3 0%, #30c7ec 47%, #46aef7 100%)"
  },
  {
    "id": "10",
    "name": "Awesome_Pine",
    "chineseName": "出色的松树",
    "cssStyle": "linear-gradient(to top, #ebbba7 0%, #cfc7f8 100%)"
  },
  {
    "id": "11",
    "name": "Big_Mango",
    "chineseName": "大芒果",
    "cssStyle": "linear-gradient(to top, #c71d6f 0%, #d09693 100%)"
  },
  {
    "id": "12",
    "name": "Black_Sea",
    "chineseName": "黑海",
    "cssStyle": "linear-gradient(-225deg, #2CD8D5 0%, #6B8DD6 48%, #8E37D7 100%)"
  },
  {
    "id": "13",
    "name": "Blessing",
    "chineseName": "祝福",
    "cssStyle": "linear-gradient(to top, #fddb92 0%, #d1fdff 100%)"
  },
  {
    "id": "14",
    "name": "Burning_Spring",
    "chineseName": "燃烧的春天",
    "cssStyle": "linear-gradient(to top, #4fb576 0%, #44c489 30%, #28a9ae 46%, #28a2b7 59%, #4c7788 71%, #6c4f63 86%, #432c39 100%)"
  },
  {
    "id": "15",
    "name": "Cheerful_Caramel",
    "chineseName": "快乐焦糖",
    "cssStyle": "linear-gradient(to top, #e6b980 0%, #eacda3 100%)"
  },
  {
    "id": "16",
    "name": "Child_Care",
    "chineseName": "儿童护理",
    "cssStyle": "linear-gradient(-20deg, #f794a4 0%, #fdd6bd 100%)"
  },
  {
    "id": "17",
    "name": "Clean_Mirror",
    "chineseName": "清洁镜子",
    "cssStyle": "linear-gradient(45deg, #93a5cf 0%, #e4efe9 100%)"
  },
  {
    "id": "18",
    "name": "Cloudy_Apple",
    "chineseName": "多云的苹果",
    "cssStyle": "linear-gradient(to top, #f3e7e9 0%, #e3eeff 99%, #e3eeff 100%)"
  },
  {
    "id": "19",
    "name": "Cloudy_Knoxville",
    "chineseName": "多云的诺克斯维尔",
    "cssStyle": "linear-gradient(120deg, #fdfbfb 0%, #ebedee 100%)"
  },
  {
    "id": "20",
    "name": "Cold_Evening",
    "chineseName": "寒冷的夜晚",
    "cssStyle": "linear-gradient(to top, #0c3483 0%, #a2b6df 100%, #6b8cce 100%, #a2b6df 100%)"
  },
  {
    "id": "21",
    "name": "Colorful_Peach",
    "chineseName": "多彩的桃子",
    "cssStyle": "linear-gradient(to right, #ed6ea0 0%, #ec8c69 100%)"
  },
  {
    "id": "22",
    "name": "Confident_Cloud",
    "chineseName": "自信的云",
    "cssStyle": "linear-gradient(to top, #dad4ec 0%, #dad4ec 1%, #f3e7e9 100%)"
  },
  {
    "id": "23",
    "name": "Crystal_River",
    "chineseName": "水晶河",
    "cssStyle": "linear-gradient(-225deg, #22E1FF 0%, #1D8FE1 48%, #625EB1 100%)"
  },
  {
    "id": "24",
    "name": "Crystalline",
    "chineseName": "水晶质",
    "cssStyle": "linear-gradient(-20deg, #00cdac 0%, #8ddad5 100%)"
  },
  {
    "id": "25",
    "name": "Deep_Blue",
    "chineseName": "深蓝",
    "cssStyle": "linear-gradient(120deg, #e0c3fc 0%, #8ec5fc 100%)"
  },
  {
    "id": "26",
    "name": "Deep_Blue",
    "chineseName": "深蓝",
    "cssStyle": "linear-gradient(to right, #6a11cb 0%, #2575fc 100%)"
  },
  {
    "id": "27",
    "name": "Deep_Relief",
    "chineseName": "深层舒缓",
    "cssStyle": "linear-gradient(-225deg, #7085B6 0%, #87A7D9 50%, #DEF3F8 100%)"
  },
  {
    "id": "28",
    "name": "Dense_Water",
    "chineseName": "密集的水",
    "cssStyle": "linear-gradient(to right, #3ab5b0 0%, #3d99be 31%, #56317a 100%)"
  },
  {
    "id": "29",
    "name": "Desert_Hump",
    "chineseName": "沙漠驼峰",
    "cssStyle": "linear-gradient(to top, #c79081 0%, #dfa579 100%)"
  },
  {
    "id": "30",
    "name": "Dirty_Beauty",
    "chineseName": "脏污的美丽",
    "cssStyle": "linear-gradient(to top, #6a85b6 0%, #bac8e0 100%)"
  },
  {
    "id": "31",
    "name": "Dusty_Grass",
    "chineseName": "尘土飞扬的草地",
    "cssStyle": "linear-gradient(120deg, #d4fc79 0%, #96e6a1 100%)"
  },
  {
    "id": "32",
    "name": "Eternal_Constance",
    "chineseName": "永恒的恒心",
    "cssStyle": "linear-gradient(to top, #09203f 0%, #537895 100%)"
  },
  {
    "id": "33",
    "name": "Everlasting_Sky",
    "chineseName": "永恒的天空",
    "cssStyle": "linear-gradient(135deg, #fdfcfb 0%, #e2d1c3 100%)"
  },
  {
    "id": "34",
    "name": "Fabled_Sunset",
    "chineseName": "传说中的日落",
    "cssStyle": "linear-gradient(-225deg, #231557 0%, #44107A 29%, #FF1361 67%, #FFF800 100%)"
  },
  {
    "id": "35",
    "name": "Faraway_River",
    "chineseName": "遥远的河流",
    "cssStyle": "linear-gradient(-20deg, #6e45e2 0%, #88d3ce 100%)"
  },
  {
    "id": "36",
    "name": "February_Ink",
    "chineseName": "二月墨水",
    "cssStyle": "linear-gradient(to top, #accbee 0%, #e7f0fd 100%)"
  },
  {
    "id": "37",
    "name": "Fly_High",
    "chineseName": "高飞",
    "cssStyle": "linear-gradient(to top, #48c6ef 0%, #6f86d6 100%)"
  },
  {
    "id": "38",
    "name": "Flying_Lemon",
    "chineseName": "飞翔的柠檬",
    "cssStyle": "linear-gradient(60deg, #64b3f4 0%, #c2e59c 100%)"
  },
  {
    "id": "39",
    "name": "Forest_Inei",
    "chineseName": "森林伊内",
    "cssStyle": "linear-gradient(to top, #df89b5 0%, #bfd9fe 100%)"
  },
  {
    "id": "40",
    "name": "Fresh_Milk",
    "chineseName": "新鲜牛奶",
    "cssStyle": "linear-gradient(to top, #feada6 0%, #f5efef 100%)"
  },
  {
    "id": "41",
    "name": "Fresh_Oasis",
    "chineseName": "新鲜绿洲",
    "cssStyle": "linear-gradient(-225deg, #7DE2FC 0%, #B9B6E5 100%)"
  },
  {
    "id": "42",
    "name": "Frozen_Berry",
    "chineseName": "冷冻浆果",
    "cssStyle": "linear-gradient(to top, #e8198b 0%, #c7eafd 100%)"
  },
  {
    "id": "43",
    "name": "Frozen_Dreams",
    "chineseName": "冻结的梦想",
    "cssStyle": "linear-gradient(to top, #fdcbf1 0%, #fdcbf1 1%, #e6dee9 100%)"
  },
  {
    "id": "44",
    "name": "Frozen_Heat",
    "chineseName": "冻结的热量",
    "cssStyle": "linear-gradient(-225deg, #FF057C 0%, #7C64D5 48%, #4CC3FF 100%)"
  },
  {
    "id": "45",
    "name": "Fruit_Blend",
    "chineseName": "水果混合",
    "cssStyle": "linear-gradient(to right, #f9d423 0%, #ff4e50 100%)"
  },
  {
    "id": "46",
    "name": "Gagarin_View",
    "chineseName": "加加林视角",
    "cssStyle": "linear-gradient(-225deg, #69EACB 0%, #EACCF8 48%, #6654F1 100%)"
  },
  {
    "id": "47",
    "name": "Gentle_Care",
    "chineseName": "温柔的护理",
    "cssStyle": "linear-gradient(to right, #ffc3a0 0%, #ffafbd 100%)"
  },
  {
    "id": "48",
    "name": "Glass_Water",
    "chineseName": "玻璃水",
    "cssStyle": "linear-gradient(to top, #dfe9f3 0%, white 100%)"
  },
  {
    "id": "49",
    "name": "Grass_Shampoo",
    "chineseName": "草本洗发水",
    "cssStyle": "linear-gradient(-225deg, #DFFFCD 0%, #90F9C4 48%, #39F3BB 100%)"
  },
  {
    "id": "50",
    "name": "Great_Whale",
    "chineseName": "伟大的鲸鱼",
    "cssStyle": "linear-gradient(to top, #a3bded 0%, #6991c7 100%)"
  },
  {
    "id": "51",
    "name": "Grown_Early",
    "chineseName": "早熟",
    "cssStyle": "linear-gradient(to top, #0ba360 0%, #3cba92 100%)"
  },
  {
    "id": "52",
    "name": "Happy_Acid",
    "chineseName": "快乐酸",
    "cssStyle": "linear-gradient(to top, #37ecba 0%, #72afd3 100%)"
  },
  {
    "id": "53",
    "name": "Happy_Fisher",
    "chineseName": "快乐的渔夫",
    "cssStyle": "linear-gradient(120deg, #89f7fe 0%, #66a6ff 100%)"
  },
  {
    "id": "54",
    "name": "Happy_Memories",
    "chineseName": "快乐的回忆",
    "cssStyle": "linear-gradient(-60deg, #ff5858 0%, #f09819 100%)"
  },
  {
    "id": "55",
    "name": "Happy_Unicorn",
    "chineseName": "快乐的独角兽",
    "cssStyle": "linear-gradient(to top, #b3ffab 0%, #12fff7 100%)"
  },
  {
    "id": "56",
    "name": "Healthy_Water",
    "chineseName": "健康的水",
    "cssStyle": "linear-gradient(60deg, #96deda 0%, #50c9c3 100%)"
  },
  {
    "id": "57",
    "name": "Heaven_Peach",
    "chineseName": "天堂桃子",
    "cssStyle": "linear-gradient(to top, #d9afd9 0%, #97d9e1 100%)"
  },
  {
    "id": "58",
    "name": "Heavy_Rain",
    "chineseName": "大雨",
    "cssStyle": "linear-gradient(to top, #cfd9df 0%, #e2ebf0 100%)"
  },
  {
    "id": "59",
    "name": "Hidden_Jaguar",
    "chineseName": "隐藏的美洲虎",
    "cssStyle": "linear-gradient(to top, #0fd850 0%, #f9f047 100%)"
  },
  {
    "id": "60",
    "name": "High_Flight",
    "chineseName": "高飞",
    "cssStyle": "linear-gradient(to right, #0acffe 0%, #495aff 100%)"
  },
  {
    "id": "61",
    "name": "Itmeo_Branding",
    "chineseName": "Itmeo 品牌",
    "cssStyle": "linear-gradient(180deg, #2af598 0%, #009efd 100%)"
  },
  {
    "id": "62",
    "name": "Japan_Blush",
    "chineseName": "日本腮红",
    "cssStyle": "linear-gradient(-20deg, #ddd6f3 0%, #faaca8 100%, #faaca8 100%)"
  },
  {
    "id": "63",
    "name": "Juicy_Cake",
    "chineseName": "多汁的蛋糕",
    "cssStyle": "linear-gradient(to top, #e14fad 0%, #f9d423 100%)"
  },
  {
    "id": "64",
    "name": "Juicy_Peach",
    "chineseName": "多汁的桃子",
    "cssStyle": "linear-gradient(to right, #ffecd2 0%, #fcb69f 100%)"
  },
  {
    "id": "65",
    "name": "Jungle_Day",
    "chineseName": "丛林日",
    "cssStyle": "linear-gradient(45deg, #8baaaa 0%, #ae8b9c 100%)"
  },
  {
    "id": "66",
    "name": "Kind_Steel",
    "chineseName": "亲切的钢铁",
    "cssStyle": "linear-gradient(-20deg, #e9defa 0%, #fbfcdb 100%)"
  },
  {
    "id": "67",
    "name": "Ladoga_Bottom",
    "chineseName": "拉多加底部",
    "cssStyle": "linear-gradient(to top, #ebc0fd 0%, #d9ded8 100%)"
  },
  {
    "id": "68",
    "name": "Lady_Lips",
    "chineseName": "女士嘴唇",
    "cssStyle": "linear-gradient(to top, #ff9a9e 0%, #fecfef 99%, #fecfef 100%)"
  },
  {
    "id": "69",
    "name": "Landing_Aircraft",
    "chineseName": "着陆飞机",
    "cssStyle": "linear-gradient(-225deg, #5D9FFF 0%, #B8DCFF 48%, #6BBBFF 100%)"
  },
  {
    "id": "70",
    "name": "Le_Cocktail",
    "chineseName": "Le 鸡尾酒",
    "cssStyle": "linear-gradient(45deg, #874da2 0%, #c43a30 100%)"
  },
  {
    "id": "71",
    "name": "Lemon_Gate",
    "chineseName": "柠檬门",
    "cssStyle": "linear-gradient(to top, #96fbc4 0%, #f9f586 100%)"
  },
  {
    "id": "72",
    "name": "Light_Blue",
    "chineseName": "浅蓝",
    "cssStyle": "linear-gradient(-225deg, #9EFBD3 0%, #57E9F2 48%, #45D4FB 100%)"
  },
  {
    "id": "73",
    "name": "Lily_Meadow",
    "chineseName": "百合草地",
    "cssStyle": "linear-gradient(-225deg, #65379B 0%, #886AEA 53%, #6457C6 100%)"
  },
  {
    "id": "74",
    "name": "Love_Kiss",
    "chineseName": "爱的吻",
    "cssStyle": "linear-gradient(to top, #ff0844 0%, #ffb199 100%)"
  },
  {
    "id": "75",
    "name": "Magic_Lake",
    "chineseName": "魔法湖",
    "cssStyle": "linear-gradient(to top, #d5dee7 0%, #ffafbd 0%, #c9ffbf 100%)"
  },
  {
    "id": "76",
    "name": "Magic_Ray",
    "chineseName": "魔法射线",
    "cssStyle": "linear-gradient(-225deg, #FF3CAC 0%, #562B7C 52%, #2B86C5 100%)"
  },
  {
    "id": "77",
    "name": "Malibu_Beach",
    "chineseName": "马里布海滩",
    "cssStyle": "linear-gradient(to right, #4facfe 0%, #00f2fe 100%)"
  },
  {
    "id": "78",
    "name": "Marble_Wall",
    "chineseName": "大理石墙",
    "cssStyle": "linear-gradient(to top, #bdc2e8 0%, #bdc2e8 1%, #e6dee9 100%)"
  },
  {
    "id": "79",
    "name": "Mars_Party",
    "chineseName": "火星派对",
    "cssStyle": "linear-gradient(to top, #5f72bd 0%, #9b23ea 100%)"
  },
  {
    "id": "80",
    "name": "Mean_Fruit",
    "chineseName": "恶意水果",
    "cssStyle": "linear-gradient(120deg, #fccb90 0%, #d57eeb 100%)"
  },
  {
    "id": "81",
    "name": "Midnight_Bloom",
    "chineseName": "午夜绽放",
    "cssStyle": "linear-gradient(-20deg, #2b5876 0%, #4e4376 100%)"
  },
  {
    "id": "82",
    "name": "Millennium_Pine",
    "chineseName": "千禧松",
    "cssStyle": "linear-gradient(to top, #50cc7f 0%, #f5d100 100%)"
  },
  {
    "id": "83",
    "name": "Mind_Crawl",
    "chineseName": "心灵爬行",
    "cssStyle": "linear-gradient(-225deg, #473B7B 0%, #3584A7 51%, #30D2BE 100%)"
  },
  {
    "id": "84",
    "name": "Mixed_Hopes",
    "chineseName": "混合希望",
    "cssStyle": "linear-gradient(to top, #c471f5 0%, #fa71cd 100%)"
  },
  {
    "id": "85",
    "name": "Morpheus_Den",
    "chineseName": "墨菲斯的巢穴",
    "cssStyle": "linear-gradient(to top, #30cfd0 0%, #91a7ff 100%)"
  },
  {
    "id": "86",
    "name": "Mountain_Rock",
    "chineseName": "山岩",
    "cssStyle": "linear-gradient(to right, #868f96 0%, #596164 100%)"
  },
  {
    "id": "87",
    "name": "Near_Moon",
    "chineseName": "近月",
    "cssStyle": "linear-gradient(to top, #5ee7df 0%, #66a6ff 100%)"
  },
  {
    "id": "88",
    "name": "New_Life",
    "chineseName": "新生活",
    "cssStyle": "linear-gradient(to right, #43e97b 0%, #38f9d7 100%)"
  },
  {
    "id": "89",
    "name": "New_York",
    "chineseName": "纽约",
    "cssStyle": "linear-gradient(to top, #ff6b6b 0%, #feca57 100%)"
  },
  {
    "id": "90",
    "name": "Night_Call",
    "chineseName": "夜间通话",
    "cssStyle": "linear-gradient(to top, #ac32e4 0%, #7918f2 48%, #4801ff 100%)"
  },
  {
    "id": "91",
    "name": "Night_Fade",
    "chineseName": "夜色渐逝",
    "cssStyle": "linear-gradient(to top, #a18cd1 0%, #fbc2eb 100%)"
  },
  {
    "id": "92",
    "name": "Night_Party",
    "chineseName": "夜间派对",
    "cssStyle": "linear-gradient(to top, #0250c5 0%, #d43f8d 100%)"
  },
  {
    "id": "93",
    "name": "North_Miracle",
    "chineseName": "北方奇迹",
    "cssStyle": "linear-gradient(to right, #00dbde 0%, #fc00ff 100%)"
  },
  {
    "id": "94",
    "name": "Oceanic",
    "chineseName": "海洋的",
    "cssStyle": "linear-gradient(to top, #6a3093 0%, #a044ff 100%)"
  },
  {
    "id": "95",
    "name": "Orange_Coral",
    "chineseName": "橙色珊瑚",
    "cssStyle": "linear-gradient(-225deg, #FF7F50 0%, #FF1361 100%)"
  },
  {
    "id": "96",
    "name": "Orange_Fun",
    "chineseName": "橙色乐趣",
    "cssStyle": "linear-gradient(45deg, #fc466b 0%, #3f5efb 100%)"
  },
  {
    "id": "97",
    "name": "Over_Sun",
    "chineseName": "超越太阳",
    "cssStyle": "linear-gradient(60deg, #abecd6 0%, #fbed96 100%)"
  },
  {
    "id": "98",
    "name": "Party_Bliss",
    "chineseName": "派对极乐",
    "cssStyle": "linear-gradient(to top, #4481eb 0%, #04befe 100%)"
  },
  {
    "id": "99",
    "name": "Passionate_Bed",
    "chineseName": "激情之床",
    "cssStyle": "linear-gradient(to right, #ff758c 0%, #ff7eb3 100%)"
  },
  {
    "id": "100",
    "name": "Perfect_Blue",
    "chineseName": "完美蓝",
    "cssStyle": "linear-gradient(-225deg, #7742B2 0%, #f180ff 52%, #fd8bd9 100%)"
  },
  {
    "id": "101",
    "name": "Perfect_White",
    "chineseName": "完美白",
    "cssStyle": "linear-gradient(-225deg, #E3FDF5 0%, #FFE6FA 100%)"
  },
  {
    "id": "102",
    "name": "Phoenix_Start",
    "chineseName": "凤凰开始",
    "cssStyle": "linear-gradient(to right, #f83600 0%, #f9d423 100%)"
  },
  {
    "id": "103",
    "name": "Pink_Flavour",
    "chineseName": "粉色风味",
    "cssStyle": "linear-gradient(45deg, #874da2 0%, #c43a30 100%)"
  },
  {
    "id": "104",
    "name": "Plum_Plate",
    "chineseName": "梅花盘",
    "cssStyle": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
  },
  {
    "id": "105",
    "name": "Premium_Dark",
    "chineseName": "高级黑",
    "cssStyle": "linear-gradient(to right, #434343 0%, black 100%)"
  },
  {
    "id": "106",
    "name": "Premium_White",
    "chineseName": "高级白",
    "cssStyle": "linear-gradient(to top, #d5d4d0 0%, #d5d4d0 1%, #eeeeec 31%, #efeeec 75%, #e9e9e7 100%)"
  },
  {
    "id": "107",
    "name": "Purple_Division",
    "chineseName": "紫色分割",
    "cssStyle": "linear-gradient(to top, #7028e4 0%, #e5b2ca 100%)"
  },
  {
    "id": "108",
    "name": "Purple_Love",
    "chineseName": "紫色爱情",
    "cssStyle": "linear-gradient(to top, #cc208e 0%, #6713d2 100%)"
  },
  {
    "id": "109",
    "name": "Rare_Wind",
    "chineseName": "稀有之风",
    "cssStyle": "linear-gradient(to top, #a8edea 0%, #fed6e3 100%)"
  },
  {
    "id": "110",
    "name": "Red_Salvation",
    "chineseName": "红色救赎",
    "cssStyle": "linear-gradient(to top, #f43b47 0%, #453a94 100%)"
  },
  {
    "id": "111",
    "name": "Rich_Metal",
    "chineseName": "丰富金属",
    "cssStyle": "linear-gradient(to right, #d5dee7 0%, #ffafbd 0%, #c9ffbf 100%)"
  },
  {
    "id": "112",
    "name": "River_City",
    "chineseName": "河城",
    "cssStyle": "linear-gradient(to top, #4481eb 0%, #04befe 100%)"
  },
  {
    "id": "113",
    "name": "Royal_Garden",
    "chineseName": "皇家花园",
    "cssStyle": "linear-gradient(to right, #ed6ea0 0%, #ec8c69 100%)"
  },
  {
    "id": "114",
    "name": "Saint_Petersburg",
    "chineseName": "圣彼得堡",
    "cssStyle": "linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)"
  },
  {
    "id": "115",
    "name": "Sand_Strike",
    "chineseName": "沙击",
    "cssStyle": "linear-gradient(to top, #c1dfc4 0%, #deecdd 100%)"
  },
  {
    "id": "116",
    "name": "Sea_Lord",
    "chineseName": "海王",
    "cssStyle": "linear-gradient(-225deg, #2CD8D5 0%, #C5C1FF 56%, #FFBAC3 100%)"
  },
  {
    "id": "117",
    "name": "Seashore",
    "chineseName": "海岸",
    "cssStyle": "linear-gradient(to top, #209cff 0%, #68e0cf 100%)"
  },
  {
    "id": "118",
    "name": "Shadow_Night",
    "chineseName": "暗夜",
    "cssStyle": "linear-gradient(to top, #000000 0%, #434343 100%)"
  },
  {
    "id": "119",
    "name": "Shiny_Rainbow",
    "chineseName": "闪亮彩虹",
    "cssStyle": "linear-gradient(to right, #00c6ff 0%, #0072ff 100%)"
  },
  {
    "id": "120",
    "name": "Sky_Glider",
    "chineseName": "天空滑翔机",
    "cssStyle": "linear-gradient(to top, #88d3ce 0%, #6e45e2 100%)"
  },
  {
    "id": "121",
    "name": "Slick_Carbon",
    "chineseName": "光滑碳",
    "cssStyle": "linear-gradient(to top, #0c0c0c 0%, #141e30 100%)"
  },
  {
    "id": "122",
    "name": "Smiling_Rain",
    "chineseName": "微笑雨",
    "cssStyle": "linear-gradient(-225deg, #dcb0ed 0%, #99c99c 100%)"
  },
  {
    "id": "123",
    "name": "Snow_Again",
    "chineseName": "再次下雪",
    "cssStyle": "linear-gradient(to top, #e6e9f0 0%, #eef1f5 100%)"
  },
  {
    "id": "124",
    "name": "Soft_Grass",
    "chineseName": "柔软草地",
    "cssStyle": "linear-gradient(to top, #c1dfc4 0%, #deecdd 100%)"
  },
  {
    "id": "125",
    "name": "Solid_Stone",
    "chineseName": "坚固石头",
    "cssStyle": "linear-gradient(to right, #243949 0%, #517fa4 100%)"
  },
  {
    "id": "126",
    "name": "Space_Shift",
    "chineseName": "空间转换",
    "cssStyle": "linear-gradient(60deg, #3d3393 0%, #2b76b9 37%, #2cacd1 65%, #35eb93 100%)"
  },
  {
    "id": "127",
    "name": "Spring_Warmth",
    "chineseName": "春日暖意",
    "cssStyle": "linear-gradient(to top, #fad0c4 0%, #fad0c4 1%, #ffd1ff 100%)"
  },
  {
    "id": "128",
    "name": "Star_Wine",
    "chineseName": "星酒",
    "cssStyle": "linear-gradient(to right, #b8cbb8 0%, #b8cbb8 0%, #b465da 0%, #cf6cc9 33%, #ee609c 66%, #ee609c 100%)"
  },
  {
    "id": "129",
    "name": "Strong_Bliss",
    "chineseName": "强烈极乐",
    "cssStyle": "linear-gradient(to right, #f78ca0 0%, #f9748f 19%, #fd868c 60%, #fe9a8b 100%)"
  },
  {
    "id": "130",
    "name": "Strong_Stick",
    "chineseName": "强力棒",
    "cssStyle": "linear-gradient(to right, #a8caba 0%, #5d4e75 100%)"
  },
  {
    "id": "131",
    "name": "Summer_Games",
    "chineseName": "夏季游戏",
    "cssStyle": "linear-gradient(to right, #92fe9d 0%, #00c9ff 100%)"
  },
  {
    "id": "132",
    "name": "Sun_Veggie",
    "chineseName": "阳光蔬菜",
    "cssStyle": "linear-gradient(to top, #20E2D7 0%, #F9FEA5 100%)"
  },
  {
    "id": "133",
    "name": "Sunny_Morning",
    "chineseName": "阳光早晨",
    "cssStyle": "linear-gradient(-225deg, #FFFEFF 0%, #D7FFFE 100%)"
  },
  {
    "id": "134",
    "name": "Supreme_Sky",
    "chineseName": "至高天空",
    "cssStyle": "linear-gradient(to top, #d299c2 0%, #fef9d7 100%)"
  },
  {
    "id": "135",
    "name": "Sweet_Period",
    "chineseName": "甜蜜时光",
    "cssStyle": "linear-gradient(to top, #3f51b1 0%, #5a55ae 13%, #7b5fac 25%, #8f6aae 38%, #a86aa4 50%, #cc6b8e 62%, #f18271 75%, #f3a469 87%, #f7c978 100%)"
  },
  {
    "id": "136",
    "name": "Tempting_Azure",
    "chineseName": "诱人天蓝",
    "cssStyle": "linear-gradient(120deg, #84fab0 0%, #8fd3f4 100%)"
  },
  {
    "id": "137",
    "name": "The_Strain",
    "chineseName": "紧张",
    "cssStyle": "linear-gradient(to top, #870000 0%, #190a05 100%)"
  },
  {
    "id": "138",
    "name": "True_Sunset",
    "chineseName": "真正的日落",
    "cssStyle": "linear-gradient(to right, #fa709a 0%, #fee140 100%)"
  },
  {
    "id": "139",
    "name": "Vicious_Stance",
    "chineseName": "恶毒姿态",
    "cssStyle": "linear-gradient(60deg, #29323c 0%, #485563 100%)"
  },
  {
    "id": "140",
    "name": "Warm_Flame",
    "chineseName": "温暖火焰",
    "cssStyle": "linear-gradient(45deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)"
  },
  {
    "id": "141",
    "name": "Wide_Matrix",
    "chineseName": "宽矩阵",
    "cssStyle": "linear-gradient(to top, #fcc5e4 0%, #fda34b 15%, #ff7882 35%, #c8699e 52%, #7046aa 71%, #0c1db8 87%, #020f75 100%)"
  },
  {
    "id": "142",
    "name": "Wild_Apple",
    "chineseName": "野苹果",
    "cssStyle": "linear-gradient(to top, #d299c2 0%, #fef9d7 100%)"
  },
  {
    "id": "143",
    "name": "Winter_Neva",
    "chineseName": "冬季涅瓦",
    "cssStyle": "linear-gradient(120deg, #a8edea 0%, #fed6e3 100%)"
  },
  {
    "id": "144",
    "name": "Witch_Dance",
    "chineseName": "女巫之舞",
    "cssStyle": "linear-gradient(to right, #a8caba 0%, #5d4e75 100%)"
  },
  {
    "id": "145",
    "name": "Young_Grass",
    "chineseName": "年轻的草地",
    "cssStyle": "linear-gradient(to top, #9be15d 0%, #00e3ae 100%)"
  },
  {
    "id": "146",
    "name": "Young_Passion",
    "chineseName": "年轻的激情",
    "cssStyle": "linear-gradient(to right, #ff8177 0%, #ff867a 0%, #ff8c7f 21%, #f99185 52%, #cf556c 78%, #b12a5b 100%)"
  },
  {
    "id": "147",
    "name": "Zeus_Miracle",
    "chineseName": "宙斯奇迹",
    "cssStyle": "linear-gradient(to top, #cd9cf2 0%, #f6f3ff 100%)"
  }
];

// 处理渐变色数据，提取颜色点
const processGradientData = (data) => {
  return data.map(item => {
    // 提取CSS样式中的颜色点
    const cssStyle = item.cssStyle;

    // 使用更精确的正则表达式来匹配颜色值和百分比
    const colorStops = cssStyle.match(/(#[0-9A-Fa-f]{6}|#[0-9A-Fa-f]{3}|rgba?\([^)]+\)|[a-zA-Z]+)\s+(\d+%)/g);

    let colors = [];
    if (colorStops) {
      colors = colorStops.map(stop => {
        const match = stop.match(/(#[0-9A-Fa-f]{6}|#[0-9A-Fa-f]{3}|rgba?\([^)]+\)|[a-zA-Z]+)/);
        return match ? match[1] : null;
      }).filter(color => color !== null);
    } else {
      // 如果没有找到颜色停止点，尝试匹配所有颜色值
      const allColors = cssStyle.match(/(#[0-9A-Fa-f]{6}|#[0-9A-Fa-f]{3}|rgba?\([^)]+\))/g);
      if (allColors) {
        colors = allColors;
      } else {
        // 如果仍然没有找到颜色值，尝试匹配颜色关键字
        const colorKeywords = cssStyle.match(/\b(red|green|blue|yellow|purple|cyan|magenta|lime|olive|navy|teal|aqua|fuchsia|silver|gray|maroon|white|black|orange|pink|brown|violet|indigo|gold|turquoise|lavender|beige|ivory|khaki|coral|salmon|tan|plum|orchid|thistle)\b/g);
        if (colorKeywords) {
          // 过滤掉可能的方向关键字
          colors = colorKeywords.filter(keyword =>
            !['to', 'top', 'bottom', 'left', 'right', 'at', 'center', 'linear', 'radial', 'gradient'].includes(keyword.toLowerCase())
          );
        }
      }
    }

    // 去重颜色点
    const uniqueColors = [...new Set(colors)];

    // 返回处理后的数据
    return {
      ...item,
      colors: uniqueColors,
      englishName: item.name.replace(/_/g, ' ')
    };
  });
};

// 导出处理后的渐变色数据
module.exports = {
  rawData: GRADIENT_DATA,
  processedData: processGradientData(GRADIENT_DATA)
};
