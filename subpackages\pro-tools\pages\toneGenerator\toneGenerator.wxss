/* pages/toneGenerator/toneGenerator.wxss */
.container {
  padding: 30rpx;
  box-sizing: border-box;
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 顶部颜色选择区域 */
.section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 14rpx;
  position: relative;
  padding-left: 18rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background-color: #07c160;
  border-radius: 3rpx;
}

.input-section {
  padding-bottom: 24rpx;
}

.base-color-container {
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  padding: 4rpx 0 0;
  flex-wrap: nowrap;
  justify-content: space-between;
  gap: 0;
  overflow: hidden;
  margin-bottom: 0;
}

.color-preview {
  width: 63%;
  height: 134rpx;
  border-radius: 10rpx;
  margin-right: 0;
  border: none;
  flex: 0.63;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 0;
  margin-top: -2rpx;
  box-sizing: border-box;
  margin-bottom: 0;
}

.color-preview:active {
  transform: scale(0.98);
  opacity: 0.95;
}

/* 色块中的16进制色值 */
.color-hex-value {
  font-size: 28rpx;
  font-family: 'Courier New', monospace;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  text-align: center;
  max-width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0.5rpx;
  font-weight: 500;
  /* 文字颜色通过内联样式动态设置 */
  /* 半透明背景，使文字更易读 */
  background-color: rgba(0, 0, 0, 0.15);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

/* 右侧上下并列的按钮容器 */
.color-actions-column {
  width: 38%;
  flex: 0.38;
  display: flex;
  flex-direction: column;
  gap: 14rpx;
  min-width: 180rpx;
  max-width: 220rpx;
  margin-left: 0;
  margin-bottom: 0;
}

/* 按钮包装器 */
.btn-wrapper {
  width: 100%;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: -1rpx;
  box-sizing: border-box;
  position: relative;
}

.custom-btn {
  width: 100%;
  font-size: 22rpx;
  padding: 0 4rpx;
  background-color: #f5f5f5;
  color: #333333;
  border-radius: 6rpx;
  border: none;
  line-height: 60rpx;
  height: 60rpx;
  transition: all 0.2s ease;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  font-weight: 500;
  position: relative;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  letter-spacing: -0.8rpx;
  text-align: center;
  margin: 0;
  display: block;
  box-sizing: border-box;
  cursor: pointer;
}

/* 随机颜色按钮 */
.random-btn {
  background-color: #f0f0f0;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 选择颜色按钮 */
.picker-btn {
  background-color: #00CC66;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 204, 102, 0.3);
}

.custom-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 色调展示区域 */
.result-section {
  padding-bottom: 20rpx;
  padding-left: 16rpx;
  padding-right: 16rpx;
}

.result-intro {
  font-size: 22rpx;
  color: #888;
  margin-bottom: 12rpx;
  text-align: center;
  padding: 0 10rpx;
}

.tones-container {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.03);
  width: 70%;
  margin-left: auto;
  margin-right: auto;
}

.tone-item {
  display: flex;
  align-items: center;
  height: 72rpx;
  transition: all 0.2s ease;
}

.tone-item:active {
  background-color: rgba(0, 0, 0, 0.02);
}

.tone-color {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.tone-color:active {
  opacity: 0.8;
}

.tone-color-code {
  font-size: 24rpx;
  font-family: 'Consolas', 'SF Mono', 'Menlo', 'Monaco', 'Courier New', monospace;
  /* 颜色通过内联样式动态设置 */
  /* 去掉半透明背景 */
  padding: 2rpx 10rpx;
  letter-spacing: 0.5rpx;
  font-weight: 500;
}

.tone-level {
  width: 45rpx;
  text-align: center;
  font-size: 20rpx;
  color: #999999;
  background-color: #f5f5f5;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1rpx solid rgba(0, 0, 0, 0.03);
  font-family: 'Arial', sans-serif;
  font-weight: 500;
}



/* 颜色选择器弹窗 */
.color-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.color-picker-container {
  width: 90%;
  max-width: 650rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
}

.color-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 28rpx;
  border-bottom: 1rpx solid #eeeeee;
  background-color: #fafafa;
}

.color-picker-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #191919;
}

.color-picker-close {
  font-size: 36rpx;
  color: #666666;
  line-height: 1;
  padding: 10rpx;
  margin: -10rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
