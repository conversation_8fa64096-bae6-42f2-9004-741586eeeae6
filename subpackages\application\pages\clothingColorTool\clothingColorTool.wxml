<view class="container" aria-role="main" aria-label="穿搭配色工具">
  <!-- 固定位置的预览区域 -->
  <view class="preview-section fixed" aria-role="region" aria-label="服装预览区域">
    <view class="template-selector" aria-role="navigation">
      <picker bindchange="onTemplateChange" value="{{templateIndex}}" range="{{templateNames}}" aria-label="选择角色模板">
        <view class="picker-btn" aria-role="button">
          <text>角色模板</text>
          <text class="picker-arrow" aria-hidden="true">▾</text>
        </view>
      </picker>

      <!-- 模板选择按钮下方的提示文字 -->
      <view class="coming-soon-tip" aria-label="更多模板提示">
        <view class="tip-line">更多模板开发中</view>
        <view class="tip-line">敬请期待...</view>
      </view>
    </view>
    <view class="image-container" bindtouchstart="touchStart" bindtouchend="touchEnd" id="svgContainer" aria-label="服装模型展示">
      <image src="{{svgImageSrc}}" mode="aspectFit" class="svg-image" bindload="onImageLoad" id="svgImage" bindtap="previewImage" aria-label="点击查看大图"></image>

      <!-- 滑动提示指引 -->
      <view class="swipe-guide {{hideGuide ? 'hide' : ''}}" aria-role="tooltip" aria-label="左右滑动切换模板提示">
        <view class="swipe-icon left" aria-hidden="true">
          <view class="arrow">←</view>
        </view>
        <view class="swipe-text">左右滑动切换模板</view>
        <view class="swipe-icon right" aria-hidden="true">
          <view class="arrow">→</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 预览区域的占位，确保滚动内容正确定位 -->
  <view class="preview-placeholder" aria-hidden="true"></view>

  <!-- 可滚动的控制区域 -->
  <view class="controls-section" aria-role="region" aria-label="颜色控制区域">


    <!-- 男孩模板颜色控制 -->
    <view class="color-grid" wx:if="{{currentTemplate === 'boy01'}}" aria-role="group" aria-label="男孩1模板颜色控制">
      <view class="color-block" aria-role="group" aria-label="衣服颜色控制">
        <view class="label-container">
          <text class="color-label" aria-role="heading">衣服</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="boyShirt" aria-role="button" aria-label="选择衣服颜色，当前颜色{{boyShirtColor}}">
          <view class="color-dot" style="background-color: {{boyShirtColor}}" aria-hidden="true"></view>
          <text class="color-code">{{boyShirtColor}}</text>
        </view>
      </view>

      <view class="color-block" aria-role="group" aria-label="裤子颜色控制">
        <view class="label-container">
          <text class="color-label" aria-role="heading">裤子</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="boyPants" aria-role="button" aria-label="选择裤子颜色，当前颜色{{boyPantsColor}}">
          <view class="color-dot" style="background-color: {{boyPantsColor}}" aria-hidden="true"></view>
          <text class="color-code">{{boyPantsColor}}</text>
        </view>
      </view>

      <view class="color-block" aria-role="group" aria-label="袜子颜色控制">
        <view class="label-container">
          <text class="color-label" aria-role="heading">袜子</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="boySocks" aria-role="button" aria-label="选择袜子颜色，当前颜色{{boySocksColor}}">
          <view class="color-dot" style="background-color: {{boySocksColor}}" aria-hidden="true"></view>
          <text class="color-code">{{boySocksColor}}</text>
        </view>
      </view>
    </view>

    <!-- 女孩1模板颜色控制 -->
    <view class="color-grid" wx:if="{{currentTemplate === 'girl01'}}">
      <view class="color-block">
        <view class="label-container">
          <text class="color-label">衣服</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="girl1Dress">
          <view class="color-dot" style="background-color: {{girl1DressColor}}"></view>
          <text class="color-code">{{girl1DressColor}}</text>
        </view>
      </view>

      <view class="color-block">
        <view class="label-container">
          <text class="color-label">裤子</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="girl1Pants">
          <view class="color-dot" style="background-color: {{girl1PantsColor}}"></view>
          <text class="color-code">{{girl1PantsColor}}</text>
        </view>
      </view>

      <view class="color-block">
        <view class="label-container">
          <text class="color-label">袜子</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="girl1Socks">
          <view class="color-dot" style="background-color: {{girl1SocksColor}}"></view>
          <text class="color-code">{{girl1SocksColor}}</text>
        </view>
      </view>

      <view class="color-block">
        <view class="label-container">
          <text class="color-label">鞋子</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="girl1Shoes">
          <view class="color-dot" style="background-color: {{girl1ShoesColor}}"></view>
          <text class="color-code">{{girl1ShoesColor}}</text>
        </view>
      </view>
    </view>

    <!-- 女孩2模板颜色控制 -->
    <view class="color-grid" wx:if="{{currentTemplate === 'girl02'}}">
      <view class="color-block">
        <view class="label-container">
          <text class="color-label">衣服</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="girlDress">
          <view class="color-dot" style="background-color: {{girlDressColor}}"></view>
          <text class="color-code">{{girlDressColor}}</text>
        </view>
      </view>

      <view class="color-block">
        <view class="label-container">
          <text class="color-label">长裙</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="girlSkirt">
          <view class="color-dot" style="background-color: {{girlSkirtColor}}"></view>
          <text class="color-code">{{girlSkirtColor}}</text>
        </view>
      </view>

      <view class="color-block">
        <view class="label-container">
          <text class="color-label">鞋子</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="girlShoes">
          <view class="color-dot" style="background-color: {{girlShoesColor}}"></view>
          <text class="color-code">{{girlShoesColor}}</text>
        </view>
      </view>
    </view>

    <!-- 男孩2模板颜色控制 -->
    <view class="color-grid" wx:if="{{currentTemplate === 'boy02'}}">
      <view class="color-block">
        <view class="label-container">
          <text class="color-label">T恤</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="boy2Shirt">
          <view class="color-dot" style="background-color: {{boy2ShirtColor}}"></view>
          <text class="color-code">{{boy2ShirtColor}}</text>
        </view>
      </view>

      <view class="color-block">
        <view class="label-container">
          <text class="color-label">外套</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="boy2Jacket">
          <view class="color-dot" style="background-color: {{boy2JacketColor}}"></view>
          <text class="color-code">{{boy2JacketColor}}</text>
        </view>
      </view>

      <view class="color-block">
        <view class="label-container">
          <text class="color-label">裤子</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="boy2Pants">
          <view class="color-dot" style="background-color: {{boy2PantsColor}}"></view>
          <text class="color-code">{{boy2PantsColor}}</text>
        </view>
      </view>

      <view class="color-block">
        <view class="label-container">
          <text class="color-label">鞋子</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="boy2Shoes">
          <view class="color-dot" style="background-color: {{boy2ShoesColor}}"></view>
          <text class="color-code">{{boy2ShoesColor}}</text>
        </view>
      </view>
    </view>



    <!-- 女孩3模板颜色控制 -->
    <view class="color-grid" wx:if="{{currentTemplate === 'girl03'}}">
      <view class="color-block">
        <view class="label-container">
          <text class="color-label">内搭</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="girl3Inner">
          <view class="color-dot" style="background-color: {{girl3InnerColor}}"></view>
          <text class="color-code">{{girl3InnerColor}}</text>
        </view>
      </view>

      <view class="color-block">
        <view class="label-container">
          <text class="color-label">开衫</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="girl3Fengyi">
          <view class="color-dot" style="background-color: {{girl3FengyiColor}}"></view>
          <text class="color-code">{{girl3FengyiColor}}</text>
        </view>
      </view>

      <view class="color-block">
        <view class="label-container">
          <text class="color-label">裙子</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="girl3Skirt">
          <view class="color-dot" style="background-color: {{girl3SkirtColor}}"></view>
          <text class="color-code">{{girl3SkirtColor}}</text>
        </view>
      </view>

      <view class="color-block">
        <view class="label-container">
          <text class="color-label">挎包</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="girl3Bag">
          <view class="color-dot" style="background-color: {{girl3BagColor}}"></view>
          <text class="color-code">{{girl3BagColor}}</text>
        </view>
      </view>

      <view class="color-block">
        <view class="label-container">
          <text class="color-label">鞋子</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="girl3Shoes">
          <view class="color-dot" style="background-color: {{girl3ShoesColor}}"></view>
          <text class="color-code">{{girl3ShoesColor}}</text>
        </view>
      </view>

      <view class="color-block">
        <view class="label-container">
          <text class="color-label">袜子</text>
        </view>
        <view class="color-selector" bindtap="showColorPicker" data-target="girl3Socks">
          <view class="color-dot" style="background-color: {{girl3SocksColor}}"></view>
          <text class="color-code">{{girl3SocksColor}}</text>
        </view>
      </view>
    </view>

  </view>

  <!-- 底部固定按钮区域 -->
  <view class="btn-container" aria-role="toolbar" aria-label="操作按钮区域">
    <view class="action-btn reset" bindtap="resetColors" aria-role="button" aria-label="重置所有颜色">重置</view>
    <view class="action-btn random" bindtap="randomColorScheme" aria-role="button" aria-label="生成随机配色方案">随机配色</view>
  </view>
</view>

<!-- 颜色选择器弹窗 -->
<view class="color-picker-modal" wx:if="{{showColorPicker}}" aria-role="dialog" aria-modal="true" aria-label="颜色选择器">
  <view class="color-picker-container" aria-role="region">
    <view class="color-picker-header" aria-role="heading">
      <view class="color-picker-title">选择{{
        currentColorTarget === 'boyShirt' ? '男孩1衣服' :
        currentColorTarget === 'boyPants' ? '男孩1裤子' :
        currentColorTarget === 'boySocks' ? '男孩1袜子' :
        currentColorTarget === 'girl1Dress' ? '女孩1衣服' :
        currentColorTarget === 'girl1Pants' ? '女孩1裤子' :
        currentColorTarget === 'girl1Socks' ? '女孩1袜子' :
        currentColorTarget === 'girl1Shoes' ? '女孩1鞋子' :
        currentColorTarget === 'girlDress' ? '女孩2衣服' :
        currentColorTarget === 'girlSkirt' ? '女孩2长裙' :
        currentColorTarget === 'girlShoes' ? '女孩2鞋子' :
        currentColorTarget === 'boy2Shirt' ? '男孩2 T恤' :
        currentColorTarget === 'boy2Jacket' ? '男孩2外套' :
        currentColorTarget === 'boy2Pants' ? '男孩2裤子' :
        currentColorTarget === 'boy2Shoes' ? '男孩2鞋子' :

        currentColorTarget === 'girl3Inner' ? '女孩3内搭' :
        currentColorTarget === 'girl3Fengyi' ? '女孩3开衫' :
        currentColorTarget === 'girl3Skirt' ? '女孩3裙子' :
        currentColorTarget === 'girl3Bag' ? '女孩3挎包' :
        currentColorTarget === 'girl3Shoes' ? '女孩3鞋子' :
        currentColorTarget === 'girl3Socks' ? '女孩3袜子' : ''
      }}颜色</view>
      <view class="color-picker-close" bindtap="hideColorPicker" aria-role="button" aria-label="关闭颜色选择器">×</view>
    </view>

    <!-- 集成公共颜色选择器组件 -->
    <color-picker
      color="{{customColor}}"
      bindchange="onColorPickerChange"
      bindconfirm="onColorPickerConfirm"
      bindcancel="hideColorPicker"
      aria-label="颜色选择器组件"
    ></color-picker>
  </view>
</view>


