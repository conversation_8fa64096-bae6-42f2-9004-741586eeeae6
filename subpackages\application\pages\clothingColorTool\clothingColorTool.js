// 导入SVG模板
const svgTemplates = require('../../data/svgTemplates/index.js');
const loadingUtils = require('../../../../utils/loadingUtils');

// 定义所有模板的默认颜色配置
const DEFAULT_COLORS = {
// boy01模板颜色配置
  boy01: {
    boyShirtColor: '#FFFFFF', // 衣服颜色 - 对应 .st13
    boyPantsColor: '#252954', // 裤子颜色 - 对应 .st0
    boySocksColor: '#111112', // 袜子颜色 - 对应 .st8
  },

  // girl01模板颜色配置
  girl01: {
    girl1DressColor: '#F65A14', // 衣服颜色 - 对应 .st5
    girl1PantsColor: '#567090', // 裤子颜色 - 对应 .st23
    girl1SocksColor: '#FFFFFF', // 袜子颜色 - 对应 .st21
    girl1ShoesColor: '#FFFFFF', // 鞋子颜色 - 对应 .st7
  },

  // girl02模板颜色配置
  girl02: {
    girlDressColor: '#A5B35C', // 衣服颜色 - 对应 .st1
    girlSkirtColor: '#151E2F', // 长裙颜色 - 对应 .st0
    girlShoesColor: '#ECC29A', // 鞋子颜色 - 对应 .st41
  },

  // boy02模板颜色配置
  boy02: {
    boy2ShirtColor: '#FEFDFD', // T恤颜色 - 对应 .st2
    boy2JacketColor: '#5B6F99', // 外套颜色 - 对应 .st0
    boy2PantsColor: '#0e0d12', // 裤子颜色 - 对应 .st1
    boy2ShoesColor: '#FFFFFF', // 鞋子颜色 - 对应 .st16
  },

  // girl03模板颜色配置
  girl03: {
    girl3InnerColor: '#FBFCFC', // 内搭颜色 - 对应 .st1
    girl3FengyiColor: '#60A8A0', // 开衫颜色 - 对应 .st0
    girl3SkirtColor: '#C8CED4', // 裙子颜色 - 对应 .st14
    girl3BagColor: '#445577', // 挎包颜色 - 对应 .st18
    girl3ShoesColor: '#FDFEFE', // 鞋子颜色 - 对应 .st16
    girl3SocksColor: '#FDFEFE', // 袜子颜色 - 对应 .st17
  }
};

Page({
  data: {
    // 初始化所有模板的颜色
    ...DEFAULT_COLORS.boy01,
    ...DEFAULT_COLORS.girl01,
    ...DEFAULT_COLORS.girl02,
    ...DEFAULT_COLORS.boy02,
    ...DEFAULT_COLORS.girl03,

    // 模板选择器数据
    templateNames: ['女孩1', '女孩2', '女孩3', '男孩1', '男孩2'],
    templateValues: ['girl01', 'girl02', 'girl03', 'boy01', 'boy02'],
    templateIndex: 0,

    // 滑动提示指引
    hideGuide: false, // 是否隐藏提示指引

    // SVG图片源
    svgImageSrc: '',

    // 颜色选择器相关
    showColorPicker: false,
    currentColorTarget: '', // 'shirt', 'pants', 'dress', 'skirt', 'socks', 'shoes'
    customColor: '',

    // 当前模板
    currentTemplate: 'girl01',

    // SVG模板列表
    templates: svgTemplates
  },

  onLoad: function() {
    // 初始化时设置正确的模板索引
    const currentTemplate = this.data.currentTemplate;
    const templateIndex = this.data.templateValues.indexOf(currentTemplate);

    if (templateIndex !== -1) {
      this.setData({
        templateIndex: templateIndex
      });
    }

    this.updateSvgImage();

    // 3秒后隐藏提示指引
    setTimeout(() => {
      this.setData({
        hideGuide: true
      });
    }, 3000);
  },

  // 触摸开始事件
  touchStart: function(e) {
    this.startX = e.touches[0].pageX;
  },

  // 触摸结束事件
  touchEnd: function(e) {
    const endX = e.changedTouches[0].pageX;
    const diff = endX - this.startX;

    // 如果滑动距离大于50，则切换模板
    if (Math.abs(diff) > 50) {
      let currentIndex = this.data.templateIndex;
      const totalTemplates = this.data.templateValues.length;

      if (diff > 0) {
        // 向右滑动，切换到上一个模板
        currentIndex = (currentIndex - 1 + totalTemplates) % totalTemplates;
      } else {
        // 向左滑动，切换到下一个模板
        currentIndex = (currentIndex + 1) % totalTemplates;
      }

      // 更新模板
      this.setData({
        templateIndex: currentIndex,
        currentTemplate: this.data.templateValues[currentIndex],
        hideGuide: true // 用户已经滑动过，隐藏提示
      });

      this.updateSvgImage();
    }
  },

  // 更新SVG图片
  updateSvgImage: function() {
    try {
      // 获取当前模板的SVG代码
      let svgCode = this.data.templates[this.data.currentTemplate];

      // 定义模板颜色映射配置
      const templateColorMap = {
        'boy01': [
          { styleClass: 'st13', colorProperty: 'boyShirtColor' },
          { styleClass: 'st0', colorProperty: 'boyPantsColor' },
          { styleClass: 'st8', colorProperty: 'boySocksColor' }
        ],
        'girl01': [
          { styleClass: 'st5', colorProperty: 'girl1DressColor' },
          { styleClass: 'st23', colorProperty: 'girl1PantsColor' },
          { styleClass: 'st21', colorProperty: 'girl1SocksColor' },
          { styleClass: 'st7', colorProperty: 'girl1ShoesColor' }
        ],
        'girl02': [
          { styleClass: 'st1', colorProperty: 'girlDressColor' },
          { styleClass: 'st0', colorProperty: 'girlSkirtColor' },
          { styleClass: 'st41', colorProperty: 'girlShoesColor' }
        ],
        'boy02': [
          { styleClass: 'st2', colorProperty: 'boy2ShirtColor' },
          { styleClass: 'st0', colorProperty: 'boy2JacketColor' },
          { styleClass: 'st1', colorProperty: 'boy2PantsColor' },
          { styleClass: 'st16', colorProperty: 'boy2ShoesColor' }
        ],
        'girl03': [
          { styleClass: 'st1', colorProperty: 'girl3InnerColor' },
          { styleClass: 'st0', colorProperty: 'girl3FengyiColor' },
          { styleClass: 'st14', colorProperty: 'girl3SkirtColor' },
          { styleClass: 'st18', colorProperty: 'girl3BagColor' },
          { styleClass: 'st16', colorProperty: 'girl3ShoesColor' },
          { styleClass: 'st17', colorProperty: 'girl3SocksColor' }
        ]
      };

      // 获取当前模板的颜色映射
      const colorMap = templateColorMap[this.data.currentTemplate];

      // 如果存在颜色映射，则应用颜色替换
      if (colorMap) {
        // 遍历颜色映射，替换对应的颜色
        colorMap.forEach(item => {
          const styleClass = item.styleClass;
          const colorValue = this.data[item.colorProperty];

          // 替换带分号的样式
          svgCode = svgCode.replace(
            new RegExp(`\\.${styleClass}\\{fill:#[0-9A-Fa-f]+;\\}`, 'g'),
            `.${styleClass}{fill:${colorValue};}`
          );

          // 替换不带分号的样式
          svgCode = svgCode.replace(
            new RegExp(`\\.${styleClass}\\{fill:#[0-9A-Fa-f]+\\}`, 'g'),
            `.${styleClass}{fill:${colorValue}}`
          );
        });
      }

      // 将SVG转换为可显示的格式
      const dataUrl = this.svgToBase64(svgCode);

      // 更新图片源
      this.setData({
        svgImageSrc: dataUrl
      });

      // 重置图片加载状态
      this.imageLoaded = false;
    } catch (error) {
      // 更新SVG图片失败
      wx.showToast({
        title: '更新图片失败',
        icon: 'none'
      });
    }
  },

  // SVG转Base64
  svgToBase64: function(svgCode) {
    // 在小程序中，使用 data:image/svg+xml;base64 前缀，并进行base64编码
    // 首先对SVG进行一些处理，确保它是有效的
    svgCode = svgCode.trim();

    // 确保SVG有正确的XML声明和SVG命名空间
    if (!svgCode.startsWith('<?xml')) {
      svgCode = '<?xml version="1.0" encoding="UTF-8" standalone="no"?>' + svgCode;
    }

    if (svgCode.indexOf('xmlns="http://www.w3.org/2000/svg"') === -1) {
      svgCode = svgCode.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"');
    }

    // 对SVG进行URL编码，确保特殊字符不会导致问题
    const encodedSvg = encodeURIComponent(svgCode)
      .replace(/'/g, '%27')
      .replace(/"/g, '%22');

    return 'data:image/svg+xml,' + encodedSvg;
  },



  // 通用颜色选择器函数
  showColorPicker: function(e) {
    // 从事件中获取目标颜色
    const target = e.currentTarget.dataset.target;

    // 根据目标获取当前颜色值
    const colorPropertyName = target + 'Color';
    const currentColor = this.data[colorPropertyName];

    // 设置颜色选择器状态
    this.setData({
      showColorPicker: true,
      currentColorTarget: target,
      customColor: currentColor
    });
  },

  // 移除冗余的颜色选择器函数，直接使用通用的showColorPicker函数

  // 隐藏颜色选择器
  hideColorPicker: function() {
    this.setData({
      showColorPicker: false
    });
  },

  // 颜色选择器变化事件 - 处理公共组件的颜色变化
  onColorPickerChange: function(e) {
    const color = e.detail.color;
    // 实时更新颜色预览，但不关闭选择器
    this.setData({
      customColor: color
    });
  },

  // 辅助函数：根据目标和颜色更新数据
  updateColorByTarget: function(target, color) {
    // 创建颜色属性映射
    const colorPropertyMap = {
      'boyShirt': 'boyShirtColor',
      'boyPants': 'boyPantsColor',
      'boySocks': 'boySocksColor',
      'girl1Dress': 'girl1DressColor',
      'girl1Pants': 'girl1PantsColor',
      'girl1Socks': 'girl1SocksColor',
      'girl1Shoes': 'girl1ShoesColor',
      'girlDress': 'girlDressColor',
      'girlSkirt': 'girlSkirtColor',
      'girlShoes': 'girlShoesColor',
      'boy2Shirt': 'boy2ShirtColor',
      'boy2Jacket': 'boy2JacketColor',
      'boy2Pants': 'boy2PantsColor',
      'boy2Shoes': 'boy2ShoesColor',
      'girl3Inner': 'girl3InnerColor',
      'girl3Fengyi': 'girl3FengyiColor',
      'girl3Skirt': 'girl3SkirtColor',
      'girl3Bag': 'girl3BagColor',
      'girl3Shoes': 'girl3ShoesColor',
      'girl3Socks': 'girl3SocksColor'
    };

    // 创建更新数据对象
    let updateData = { showColorPicker: false };

    // 如果目标在映射中存在，则更新对应的颜色属性
    if (colorPropertyMap[target]) {
      updateData[colorPropertyMap[target]] = color;
    }

    return updateData;
  },

  // 颜色选择器确认事件 - 处理公共组件的颜色确认
  onColorPickerConfirm: function(e) {
    const color = e.detail.color;
    const target = this.data.currentColorTarget;

    // 使用辅助函数获取更新数据
    const updateData = this.updateColorByTarget(target, color);

    this.setData(updateData);
    this.updateSvgImage();
  },

  // 更新自定义颜色输入
  updateCustomColor: function(e) {
    this.setData({
      customColor: e.detail.value
    });
  },

  // 应用自定义颜色
  applyCustomColor: function() {
    const color = this.data.customColor;

    // 验证颜色格式
    if (!/^#[0-9A-Fa-f]{6}$/.test(color)) {
      wx.showToast({
        title: '请输入有效的颜色代码',
        icon: 'none'
      });
      return;
    }

    const target = this.data.currentColorTarget;

    // 使用辅助函数获取更新数据
    const updateData = this.updateColorByTarget(target, color);

    this.setData(updateData);
    this.updateSvgImage();
  },

  // 模板选择器变化事件
  onTemplateChange: function(e) {
    const index = e.detail.value;
    const template = this.data.templateValues[index];

    // 更新当前模板
    let updateData = {
      currentTemplate: template,
      templateIndex: index
    };

    // 应用该模板的默认颜色
    if (DEFAULT_COLORS[template]) {
      // 合并默认颜色到更新数据中
      updateData = {
        ...updateData,
        ...DEFAULT_COLORS[template]
      };
    }

    this.setData(updateData);
    this.updateSvgImage();
  },

  // 重置颜色
  resetColors: function() {
    // 获取当前模板
    const currentTemplate = this.data.currentTemplate;

    // 使用DEFAULT_COLORS中的配置重置颜色
    if (DEFAULT_COLORS[currentTemplate]) {
      // 直接使用对应模板的默认颜色配置
      this.setData(DEFAULT_COLORS[currentTemplate]);

      // 显示提示
      wx.showToast({
        title: '已重置为默认颜色',
        icon: 'success',
        duration: 1000
      });
    } else {
      wx.showToast({
        title: '无法重置当前模板颜色',
        icon: 'none'
      });
    }

    this.updateSvgImage();
  },

  // 图片加载完成事件
  onImageLoad: function() {
    // 图片加载完成后，可以获取图片信息
    this.imageLoaded = true;
  },

  // 显示配色参考
  showColorReference: function() {
    wx.navigateTo({
      url: '/pages/colorReference/colorReference'
    });
  },

  // 随机配色方案
  randomColorScheme: function() {
    // 预设的配色方案 - 每个角色模型有5种配色方案
    const colorSchemes = {
      'boy01': [
        // 方案1: 草绿色
        { shirt: '#FFFFFF', pants: '#7C9B6F', socks: '#FFFFFF' },
        // 方案2: 黑色
        { shirt: '#1F1C1C', pants: '#F0F0F0', socks: '#F0F0F0' },
        // 方案3: 黄蓝
        { shirt: '#FFDC73', pants: '#0141AC', socks: '#F0F0F0' },
        // 方案4: 深蓝
        { shirt: '#08234D', pants: '#CCD5DE', socks: '#FFFFFF' },
        // 方案5: 军绿色
        { shirt: '#12665E', pants: '#FFFFFF', socks: '#FFFFFF' }
      ],
      'girl01': [
        // 方案1: 紫色系
        { dress: '#AA92B5', pants: '#C5CED4', socks: '#E0E0E0', shoes: '#FFFFFF' },
        // 方案2: 上浅下深灰衣服
        { dress: '#E0E0E0', pants: '#48566B', socks: '#FFFFFF', shoes: '#E0E0E0' },
        // 方案3: 上深下浅绿色系
        { dress: '#4CAF50', pants: '#FFFFFF', socks: '#E0E0E0', shoes: '#E0E0E0' },
        // 方案4: 深浅粉色系
        { dress: '#EBC3D0', pants: '#C28498', socks: '#E0E0E0', shoes: '#E0E0E0' },
        // 方案5: 多巴胺穿搭
        { dress: '#FF9702', pants: '#73714D', socks: '#FFFFFF', shoes: '#F7CC8F' }
      ],
      'girl02': [
        // 方案1: 红色系
        { dress: '#D92833', skirt: '#F5EDED', shoes: '#E4C638' },
        // 方案2: 黑色系
        { dress: '#16151B', skirt: '#F9D382', shoes: '#FFFFFF' },
        // 方案3: 多巴胺
        { dress: '#193A74', skirt: '#F5D17D', shoes: '#F84B3A' },
        // 方案4: 浅蓝系
        { dress: '#9BB4C4', skirt: '#C5D9E9', shoes: '#F5F2EB' },
        // 方案5: 黑加粉
        { dress: '#0D080E', skirt: '#EFB9C7', shoes: '#E6EBF1' }
      ],
      'boy02': [
        // 方案1: 朴素风
        { shirt: '#FEFDFD', jacket: '#1A1B2D', pants: '#9CAEB8', shoes: '#FFFFFF' },
        // 方案2: 红色系
        { shirt: '#FFFFFF', jacket: '#B71C1C', pants: '#1A1717', shoes: '#FFFFFF' },
        // 方案3: 白色系
        { shirt: '#505B7D', jacket: '#FFFFFF', pants: '#FFFFFF', shoes: '#FFFFFF' },
        // 方案4: 清新蓝
        { shirt: '#FFFFFF', jacket: '#72E0E0', pants: '#72E0E0', shoes: '#FFFFFF' },
        // 方案5: 一身黑
        { shirt: '#2B2A2A', jacket: '#2B2A2A', pants: '#2B2A2A', shoes: '#FFFFFF' }
      ],

      'girl03': [
        // 方案1: 粉色系
        { inner: '#FEE797', fengyi: '#F29BC6', skirt: '#FEF0DD', bag: '#FDE8AF', shoes: '#D7558D', socks: '#E0E0E0' },
        // 方案2: 多巴胺
        { inner: '#DF4261', fengyi: '#5C8DC8', skirt: '#E1E5E8', bag: '#FFDC60', shoes: '#F0C75E', socks: '#FFFFFF' },
        // 方案3: 绿色系
        { inner: '#FFFFFF', fengyi: '#C4D092', skirt: '#C4D092', bag: '#FFDC60', shoes: '#FFFFFF', socks: '#D2C86E' },
        // 方案4: 浅黄系
        { inner: '#E3E7F3', fengyi: '#F4E374', skirt: '#E3E7F3', bag: '#CD98BE', shoes: '#E3E7F3', socks: '#9DBCE8' },
        // 方案5: 朴素风
        { inner: '#000000', fengyi: '#9E9E9E', skirt: '#FFFFFF', bag: '#A52A2A', shoes: '#E0E0E0', socks: '#FFFFFF' }
      ]
    };

    // 获取当前模板
    const currentTemplate = this.data.currentTemplate;

    // 获取当前模板的配色方案
    const schemes = colorSchemes[currentTemplate];

    if (!schemes) {
      wx.showToast({
        title: '无法为当前模板生成配色',
        icon: 'none'
      });
      return;
    }

    // 随机选择一个配色方案
    const randomIndex = Math.floor(Math.random() * schemes.length);
    const selectedScheme = schemes[randomIndex];

    // 根据当前模板应用配色方案
    let updateData = {};

    if (currentTemplate === 'boy01') {
      updateData = {
        boyShirtColor: selectedScheme.shirt,
        boyPantsColor: selectedScheme.pants,
        boySocksColor: selectedScheme.socks
      };
    } else if (currentTemplate === 'girl01') {
      updateData = {
        girl1DressColor: selectedScheme.dress,
        girl1PantsColor: selectedScheme.pants,
        girl1SocksColor: selectedScheme.socks,
        girl1ShoesColor: selectedScheme.shoes
      };
    } else if (currentTemplate === 'girl02') {
      updateData = {
        girlDressColor: selectedScheme.dress,
        girlSkirtColor: selectedScheme.skirt,
        girlShoesColor: selectedScheme.shoes
      };
    } else if (currentTemplate === 'boy02') {
      updateData = {
        boy2ShirtColor: selectedScheme.shirt,
        boy2JacketColor: selectedScheme.jacket,
        boy2PantsColor: selectedScheme.pants,
        boy2ShoesColor: selectedScheme.shoes
      };

    } else if (currentTemplate === 'girl03') {
      updateData = {
        girl3InnerColor: selectedScheme.inner,
        girl3FengyiColor: selectedScheme.fengyi,
        girl3SkirtColor: selectedScheme.skirt,
        girl3BagColor: selectedScheme.bag,
        girl3ShoesColor: selectedScheme.shoes,
        girl3SocksColor: selectedScheme.socks
      };
    }

    // 更新数据并刷新SVG图像
    this.setData(updateData);
    this.updateSvgImage();
  },

  // 预览图片 - 使用自定义预览页面
  previewImage: async function() {
    try {
      // 检查图片是否已加载
      if (!this.data.svgImageSrc) {
        wx.showToast({
          title: '图片尚未加载完成',
          icon: 'none'
        });
        return;
      }

      // 使用 loadingUtils 安全地管理加载状态
      await loadingUtils.withLoading(async () => {
        // 使用全局数据缓存SVG图片源，避免URL过长导致的问题
        if (!getApp().globalData) {
          getApp().globalData = {};
        }

        // 存储SVG图片源到全局数据
        getApp().globalData.tempSvgImageSrc = this.data.svgImageSrc;

        // 导航到预览页面，只传递一个标识符
        return new Promise((resolve, reject) => {
          wx.navigateTo({
            url: `/subpackages/application/pages/clothingPreview/clothingPreview?useGlobal=true`,
            success: () => {
              // 延迟一下确保页面加载
              setTimeout(resolve, 300);
            },
            fail: reject
          });
        });
      }, {
        title: '准备预览...',
        mask: true
      });
    } catch (error) {
      // 预览图片失败
      wx.showToast({
        title: '打开预览失败',
        icon: 'none'
      });
    }
  }
});
