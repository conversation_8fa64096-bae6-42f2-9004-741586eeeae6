// girl01.js - 女孩模板1，支持调整衣服(.st5)、裤子(.st23)、袜子(.st21)和鞋子(.st7)颜色
module.exports = `<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" id="GIRL01" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 800 1080" style="enable-background:new 0 0 800 1080;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#F1D8C5;}
	.st1{fill:#E4B99F;}
	.st2{fill:#323232;}
	.st3{fill:#F8F2EA;}
	.st4{fill:#CAC9C6;}
	.st5{fill:#FF6B9D;}
	.st6{fill:#4ECDC4;}
	.st7{fill:#45B7D1;}
	.st8{fill:#F8F9FA;}
	.st9{fill:#E7BDA2;}
	.st10{fill:#E7BEA5;}
	.st11{fill:#E5BDA3;}
	.st12{fill:#323232;}
	.st13{fill:#F8F2EA;}
	.st14{fill:#FBFCFA;}
	.st15{fill:#FCFDFD;}
	.st16{fill:#FFFFFF;}
	.st17{fill:#F9FAF8;}
	.st18{fill:#F7FAFC;}
	.st19{fill:#FBFDFC;}
	.st20{fill:#2A364B;}
	.st21{fill:#2A3449;}
	.st22{fill:#2D3346;}
	.st23{fill:#222C40;}
	.st24{fill:#ECA884;}
	.st25{fill:#EEA584;}
	.st26{fill:#333345;}
	.st27{fill:#383345;}
</style>

<!-- 主体轮廓 -->
<path d="M400,50c-45,0-85,25-105,65c-15,30-20,65-18,100c2,40,8,80,15,120c5,30,12,60,20,90
	c8,35,18,70,25,105c5,25,8,50,10,75c2,30,3,60,5,90c1,20,2,40,4,60c1,15,2,30,3,45
	c1,25,2,50,3,75c0,15,0,30,0,45c0,20,0,40,0,60c0,25,0,50,0,75c0,30,0,60,0,90
	c0,15,0,30,0,45c0,10,0,20,0,30h200c0-10,0-20,0-30c0-15,0-30,0-45c0-30,0-60,0-90
	c0-25,0-50,0-75c0-20,0-40,0-60c0-15,0-30,0-45c1-25,2-50,3-75c1-15,2-30,3-45
	c1-20,2-40,4-60c2-30,3-60,5-90c2-25,5-50,10-75c7-35,17-70,25-105c8-30,15-60,20-90
	c7-40,13-80,15-120c2-35-3-70-18-100C485,75,445,50,400,50z"/>

<!-- 头部和脸部 -->
<circle cx="400" cy="150" r="60" class="st0"/>
<circle cx="385" cy="140" r="6" class="st2"/>
<circle cx="415" cy="140" r="6" class="st2"/>
<path d="M390,155 Q400,165 410,155" stroke="#323232" stroke-width="2" fill="none"/>

<!-- 头发 -->
<path d="M340,120 Q400,80 460,120 Q460,160 400,150 Q340,160 340,120" class="st2"/>
<path d="M350,110 Q400,70 450,110 Q450,140 400,135 Q350,140 350,110" class="st2"/>

<!-- 身体躯干 -->
<ellipse cx="400" cy="280" rx="45" ry="80" class="st0"/>

<!-- 衣服上衣 - 可调整颜色 (.st5) -->
<path class="st5" d="M355,240c0-10,20-15,45-15s45,5,45,15v120c0,15-20,25-45,25s-45-10-45-25V240z"/>
<path class="st5" d="M350,250c-5,0-10,5-10,15v80c0,20,15,35,35,35h50c20,0,35-15,35-35v-80c0-10-5-15-10-15H350z"/>

<!-- 手臂 -->
<ellipse cx="320" cy="300" rx="15" ry="50" class="st0"/>
<ellipse cx="480" cy="300" rx="15" ry="50" class="st0"/>

<!-- 裤子/下装 - 可调整颜色 (.st23) -->
<path class="st23" d="M360,380c0-5,5-10,15-10h50c10,0,15,5,15,10v200c0,10-5,15-15,15h-50c-10,0-15-5-15-15V380z"/>
<rect x="365" y="385" width="70" height="190" rx="8" class="st23"/>

<!-- 腿部 -->
<ellipse cx="380" cy="620" rx="12" ry="40" class="st0"/>
<ellipse cx="420" cy="620" rx="12" ry="40" class="st0"/>

<!-- 袜子 - 可调整颜色 (.st21) -->
<rect x="370" y="650" width="20" height="40" rx="5" class="st21"/>
<rect x="410" y="650" width="20" height="40" rx="5" class="st21"/>

<!-- 鞋子 - 可调整颜色 (.st7) -->
<ellipse cx="380" cy="710" rx="18" ry="12" class="st7"/>
<ellipse cx="420" cy="710" rx="18" ry="12" class="st7"/>
<path class="st7" d="M362,710c0-8,8-15,18-15s18,7,18,15c0,8-8,15-18,15S362,718,362,710z"/>
<path class="st7" d="M402,710c0-8,8-15,18-15s18,7,18,15c0,8-8,15-18,15S402,718,402,710z"/>

<!-- 面部细节 -->
<path d="M375,135c2-3,6-3,8,0" stroke="#E4B99F" stroke-width="1" fill="none"/>
<path d="M415,135c2-3,6-3,8,0" stroke="#E4B99F" stroke-width="1" fill="none"/>
<circle cx="400" cy="165" r="2" fill="#FF9999"/>

<!-- 衣服细节 -->
<circle cx="400" cy="280" r="3" class="st16"/>
<circle cx="400" cy="300" r="3" class="st16"/>
<circle cx="400" cy="320" r="3" class="st16"/>
</svg>`;
