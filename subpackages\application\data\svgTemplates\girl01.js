// girl01.js - 女孩模板1
module.exports = `<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 1080">
<style type="text/css">
	.st0{fill:#F1D8C5;}
	.st1{fill:#E4B99F;}
	.st2{fill:#323232;}
	.st3{fill:#F8F2EA;}
	.st4{fill:#CAC9C6;}
	.st5{fill:#FF6B9D;}
	.st6{fill:#4ECDC4;}
	.st7{fill:#45B7D1;}
	.st8{fill:#F8F9FA;}
	.st21{fill:#2A3449;}
	.st23{fill:#222C40;}
</style>
<!-- 头部和脸部 -->
<circle cx="400" cy="200" r="80" class="st0"/>
<circle cx="380" cy="190" r="8" class="st2"/>
<circle cx="420" cy="190" r="8" class="st2"/>
<path d="M390,210 Q400,220 410,210" stroke="#323232" stroke-width="2" fill="none"/>

<!-- 身体 -->
<rect x="350" y="280" width="100" height="150" rx="20" class="st0"/>

<!-- 衣服 - 可调整颜色 -->
<rect x="340" y="290" width="120" height="140" rx="15" class="st5"/>

<!-- 裤子 - 可调整颜色 -->
<rect x="360" y="430" width="80" height="200" rx="10" class="st23"/>

<!-- 袜子 - 可调整颜色 -->
<rect x="365" y="630" width="25" height="80" class="st21"/>
<rect x="410" y="630" width="25" height="80" class="st21"/>

<!-- 鞋子 - 可调整颜色 -->
<ellipse cx="377" cy="720" rx="20" ry="15" class="st7"/>
<ellipse cx="423" cy="720" rx="20" ry="15" class="st7"/>

<!-- 手臂 -->
<rect x="320" y="300" width="20" height="100" rx="10" class="st0"/>
<rect x="460" y="300" width="20" height="100" rx="10" class="st0"/>

<!-- 头发 -->
<path d="M320,160 Q400,120 480,160 Q480,200 400,180 Q320,200 320,160" class="st2"/>
</svg>`;
