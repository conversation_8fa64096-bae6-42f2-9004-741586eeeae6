/* pages/imageColorPicker/imageColorPicker.wxss */
.page {
  width: 100%;
  height: 100vh;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止页面整体滚动 */
  position: relative; /* 为绝对定位的子元素提供参考 */
}

/* 加载状态样式 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 1000;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #999;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 错误提示样式 */
.error-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  z-index: 1000;
  padding: 40rpx;
  box-sizing: border-box;
}

.error-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #ff6b6b;
  color: white;
  font-size: 60rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.error-text {
  font-size: 30rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.error-btn {
  padding: 16rpx 40rpx;
  background-color: #f5f5f5;
  color: #333;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: 1rpx solid #e0e0e0;
}

/* 图片上传区域 */
.image-upload-section {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 40rpx;
  padding-top: 300rpx; /* 调整顶部内边距，使内容位置适中 */
  box-sizing: border-box;
  background-color: #ffffff;
}

.upload-title, .upload-button, .upload-description {
  position: relative;
}

.upload-button {
  width: 260rpx;
  height: 260rpx;
  border: 2rpx dashed #e0e0e0;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f7f7f7;
  transition: all 0.2s ease;
  position: relative;
}

.upload-button:active {
  transform: scale(0.98);
  background-color: #f0f0f0;
}

.upload-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
}

.upload-text {
  font-size: 28rpx;
  color: #666;
  margin-top: 8rpx;
}

.upload-title {
  font-size: 36rpx;
  color: #333;
  margin-bottom: 40rpx;
  text-align: center;
}

.upload-description {
  font-size: 26rpx;
  color: #999;
  margin-top: 30rpx;
  text-align: center;
  max-width: 95%;
  line-height: 1.5;
}

/* 图片图标样式 */
.icon-image {
  width: 60rpx;
  height: 60rpx;
  position: relative;
  margin: 0 auto;
}

.icon-image:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #999;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'/%3E%3Ccircle cx='8.5' cy='8.5' r='1.5'/%3E%3Cpolyline points='21 15 16 10 5 21'/%3E%3C/svg%3E");
  mask-size: contain;
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'/%3E%3Ccircle cx='8.5' cy='8.5' r='1.5'/%3E%3Cpolyline points='21 15 16 10 5 21'/%3E%3C/svg%3E");
  -webkit-mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
}

/* 图片预览区域 */
.image-preview-section {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* 操作指引样式 - 更紧凑的版本 */
.operation-guide {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 15rpx;
  background-color: #f8f8f8;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 5rpx;
}

.guide-item {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
  flex: 1;
  margin: 0 4rpx;
}

.guide-icon {
  margin-right: 4rpx;
  font-size: 24rpx;
}

.guide-text {
  font-size: 20rpx;
  color: #333;
  white-space: nowrap;
}

/* 图片容器 - 固定在顶部 */
.image-container {
  width: 100%;
  height: 700rpx; /* 减小高度，为操作指引腾出空间 */
  overflow: hidden; /* 确保布局正确 */
  background-color: #f5f5f5;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  touch-action: none; /* 防止浏览器默认的触摸行为 */
  user-select: none; /* 防止文本选择 */
  padding: 20rpx; /* 内边距 - 与JS中的计算保持一致 */
  box-sizing: border-box; /* 确保内边距不会增加容器尺寸 */
}

/* 可滚动内容区域 */
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding: 30rpx;
  box-sizing: border-box;
  height: calc(100vh - 700rpx - 60rpx); /* 减去图片容器高度和更紧凑的操作指引高度 */
  padding-bottom: 120rpx; /* 增加底部内边距，确保内容不被底部按钮遮挡 */
}

/* 操作提示样式 */
.operation-tips {
  width: 100%;
  text-align: center;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
  padding: 10rpx 0;
}

/* 操作提示 */
.usage-tip {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  padding: 16rpx 20rpx;
  margin-bottom: 16rpx;
  background-color: #fff8e6;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tip-icon {
  margin-right: 8rpx;
  font-size: 32rpx;
}

.picker-image {
  width: 100%; /* 恢复为100%，确保图片可以正确接收触摸事件 */
  height: 100%; /* 恢复为100%，确保图片可以正确接收触摸事件 */
  object-fit: contain;
  transform-origin: center;
  transition: transform 0.1s ease-out;
  display: block;
  will-change: transform; /* 优化变换性能 */
  touch-action: none; /* 防止浏览器默认的触摸行为 */
}

/* 已移除未使用的十字星指针样式 */

/* 取色结果显示 */
.picked-color-info {
  padding: 20rpx 0;
  width: 100%;
  box-sizing: border-box;
}

/* 颜色结果网格布局 */
.color-result-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 新的放大器和色块色值容器 */
.magnifier-color-container {
  display: flex;
  gap: 20rpx;
  align-items: flex-start;
}

/* 放大镜缩略图区域 - 增大尺寸 */
.color-preview-thumbnail {
  width: 220rpx; /* 从180rpx增大到220rpx */
  height: 220rpx; /* 从180rpx增大到220rpx */
  border-radius: 50%;
  overflow: hidden;
  border: 2rpx solid #e0e0e0;
  flex-shrink: 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  position: relative;
  /* 移除背景色 */
}

.magnifier-thumbnail {
  position: relative;
}

.magnifier-canvas {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50%;
  /* 移除背景色 */
  z-index: 1;
  display: block; /* 确保canvas正确显示 */
}

/* 已移除未使用的缩略图十字线样式 */

/* 右侧色块和色值容器 */
.color-info-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

/* 颜色块预览 */
.color-preview-block {
  width: 100%;
  height: 60rpx;
  position: relative;
  overflow: hidden;
  border: 1rpx solid #e0e0e0;
}

/* 添加棋盘格背景，便于查看半透明颜色 */
.color-preview-block::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(45deg, #eee 25%, transparent 25%),
    linear-gradient(-45deg, #eee 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #eee 75%),
    linear-gradient(-45deg, transparent 75%, #eee 75%);
  background-size: 16rpx 16rpx;
  background-position: 0 0, 0 8rpx, 8rpx -8rpx, -8rpx 0px;
  z-index: -1;
  opacity: 0.5;
}

/* 颜色值显示区域 */
.color-values-section {
  display: flex;
  flex-direction: column;
  gap: 12rpx; /* 从16rpx减小到12rpx，使其更紧凑 */
  margin-top: 4rpx; /* 添加顶部边距 */
}

.color-value-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 16rpx;
  width: 100%;
  box-sizing: border-box;
  background-color: #ffffff; /* 添加白色背景 */
  border: 1rpx solid #e0e0e0; /* 添加边框 */
  border-radius: 6rpx; /* 添加圆角 */
}

.color-value-text {
  font-size: 32rpx;
  font-family: monospace;
  font-weight: 500;
}

.color-value-rgb {
  color: #333;
}

.color-value-hex {
  color: #333;
  text-transform: uppercase;
}

.copy-button {
  padding: 6rpx 16rpx;
  background-color: #f0f0f0;
  color: #333;
  border-radius: 6rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: 1rpx solid #d0d0d0;
  box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

/* 移除原来的放大镜样式，使用缩略图作为放大镜 */

/* 平移提示 */
.panning-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 16rpx 30rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  z-index: 100;
  white-space: nowrap;
  animation: fadeIn 0.3s ease-out;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translate(-50%, -60%); }
  to { opacity: 1; transform: translate(-50%, -50%); }
}

/* 已移除未使用的小地图和操作提示样式 */

/* 底部占位，防止内容被固定按钮遮挡 */
.bottom-placeholder {
  height: 120rpx; /* 与底部按钮容器高度保持一致 */
  width: 100%;
}

/* 底部固定按钮容器 */
.btn-container {
  padding: 20rpx 30rpx 50rpx; /* 增加底部内边距，确保白色背景延伸到屏幕底部 */
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  bottom: 0; /* 固定在屏幕底部 */
  left: 0;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: row; /* 水平排列 */
  gap: 20rpx; /* 按钮间距 */
  justify-content: center; /* 居中显示按钮，适应只有一个按钮的情况 */
}

/* 按钮样式 */
.action-btn {
  flex: 1; /* 使用flex布局，按钮平分空间 */
  max-width: 300rpx; /* 限制最大宽度，避免单个按钮太宽 */
  padding: 18rpx 0;
  border-radius: 8rpx; /* 统一圆角 */
  font-size: 30rpx; /* 统一字体大小 */
  text-align: center;
  transition: all 0.2s ease;
  font-weight: 500;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

.action-btn.reset {
  background-color: #f5f5f5;
  color: #333333;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.action-btn.confirm {
  background-color: #1aad19; /* 使用微信绿色 */
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(26, 173, 25, 0.15); /* 统一阴影 */
}
