// boy02.js - 男孩模板2
module.exports = `<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 1080">
<style type="text/css">
	.st0{fill:#F1D8C5;}
	.st1{fill:#E4B99F;}
	.st2{fill:#323232;}
	.st3{fill:#F8F2EA;}
	.st4{fill:#CAC9C6;}
	.st5{fill:#FF6B9D;}
	.st6{fill:#4ECDC4;}
	.st7{fill:#45B7D1;}
	.st8{fill:#F8F9FA;}
	.st16{fill:#000080;}
</style>
<!-- 头部和脸部 -->
<circle cx="400" cy="200" r="80" class="st0"/>
<circle cx="380" cy="190" r="8" class="st2"/>
<circle cx="420" cy="190" r="8" class="st2"/>
<path d="M390,210 Q400,220 410,210" stroke="#323232" stroke-width="2" fill="none"/>

<!-- 身体 -->
<rect x="350" y="280" width="100" height="150" rx="20" class="st0"/>

<!-- T恤 - 可调整颜色 -->
<rect x="340" y="290" width="120" height="80" rx="15" class="st2"/>

<!-- 外套 - 可调整颜色 -->
<rect x="330" y="285" width="140" height="90" rx="20" class="st0" fill-opacity="0.8"/>

<!-- 裤子 - 可调整颜色 -->
<rect x="360" y="430" width="80" height="200" rx="10" class="st1"/>

<!-- 鞋子 - 可调整颜色 -->
<ellipse cx="377" cy="650" rx="25" ry="18" class="st16"/>
<ellipse cx="423" cy="650" rx="25" ry="18" class="st16"/>

<!-- 手臂 -->
<rect x="320" y="300" width="20" height="100" rx="10" class="st0"/>
<rect x="460" y="300" width="20" height="100" rx="10" class="st0"/>

<!-- 头发 -->
<path d="M330,170 Q400,140 470,170 Q470,190 400,180 Q330,190 330,170" class="st2"/>
</svg>`;
