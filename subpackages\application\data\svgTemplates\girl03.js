// girl03.js - 女孩模板3
module.exports = `<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 1080">
<style type="text/css">
	.st0{fill:#F1D8C5;}
	.st1{fill:#E4B99F;}
	.st2{fill:#323232;}
	.st14{fill:#FF69B4;}
	.st16{fill:#8B4513;}
	.st17{fill:#FFB6C1;}
	.st18{fill:#DDA0DD;}
</style>
<!-- 头部和脸部 -->
<circle cx="400" cy="200" r="80" class="st0"/>
<circle cx="380" cy="190" r="8" class="st2"/>
<circle cx="420" cy="190" r="8" class="st2"/>
<path d="M390,210 Q400,220 410,210" stroke="#323232" stroke-width="2" fill="none"/>

<!-- 身体 -->
<rect x="350" y="280" width="100" height="150" rx="20" class="st0"/>

<!-- 内搭 - 可调整颜色 -->
<rect x="345" y="295" width="110" height="70" rx="12" class="st1"/>

<!-- 开衫 - 可调整颜色 -->
<rect x="330" y="290" width="140" height="85" rx="18" class="st0" fill-opacity="0.7"/>

<!-- 裙子 - 可调整颜色 -->
<path d="M340,375 Q400,365 460,375 L450,580 Q400,590 350,580 Z" class="st14"/>

<!-- 挎包 - 可调整颜色 -->
<ellipse cx="480" cy="400" rx="30" ry="20" class="st18"/>
<rect x="470" y="390" width="20" height="40" class="st18"/>

<!-- 鞋子 - 可调整颜色 -->
<ellipse cx="370" cy="600" rx="25" ry="18" class="st16"/>
<ellipse cx="430" cy="600" rx="25" ry="18" class="st16"/>

<!-- 袜子 - 可调整颜色 -->
<rect x="365" y="580" width="25" height="20" class="st17"/>
<rect x="410" y="580" width="25" height="20" class="st17"/>

<!-- 手臂 -->
<rect x="320" y="300" width="20" height="100" rx="10" class="st0"/>
<rect x="460" y="300" width="20" height="100" rx="10" class="st0"/>

<!-- 头发 -->
<path d="M320,160 Q400,120 480,160 Q480,200 400,180 Q320,200 320,160" class="st2"/>
</svg>`;
