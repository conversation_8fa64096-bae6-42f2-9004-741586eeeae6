// components/color-card-a/color-card-a.js - 色卡A (1:1)
const colorUtils = require('/utils/colorUtils');

Component({
  properties: {
    colors: {
      type: Array,
      value: []
    },
    imagePath: {
      type: String,
      value: ''
    }
  },

  data: {
    canvasWidth: 1600, // 画布宽度
    canvasHeight: 2303 // 画布高度
  },

  lifetimes: {
    attached() {
      this.drawColorCard();
    }
  },

  methods: {
    async drawColorCard() {
      const { canvasWidth, canvasHeight } = this.data;
      const { colors, imagePath } = this.properties;

      if (!imagePath || colors.length === 0) return;

      try {
        // 获取Canvas节点
        const query = this.createSelectorQuery();
        const canvas = await new Promise((resolve) => {
          query.select('#colorCardCanvas')
            .fields({ node: true, size: true })
            .exec((res) => {
              resolve(res[0]);
            });
        });

        if (!canvas || !canvas.node) {
          // 获取Canvas节点失败，静默处理
          return;
        }

        const ctx = canvas.node.getContext('2d');

        // 设置Canvas的实际尺寸
        canvas.node.width = canvasWidth;
        canvas.node.height = canvasHeight;

        // 绘制白色背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        try {
          // 获取图片信息
          const res = await new Promise((resolve, reject) => {
            wx.getImageInfo({
              src: imagePath,
              success: resolve,
              fail: reject
            });
          });

          const { width: imgWidth, height: imgHeight } = res;

          // 创建图片对象
          const img = canvas.node.createImage();
          await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
            img.src = imagePath;
          });

          // 绘制标题
          ctx.fillStyle = '#333333';
          ctx.font = 'bold 48px PingFang SC, sans-serif';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText('COLOR ｜ KALA配色', canvasWidth / 2, 152 + 33.5);

          // 绘制图片 - 1:1比例
          const imageWidth = 1330;
          const imageHeight = 1330;
          const imageX = 135;
          const imageY = 325;

          this.drawSquareImage(ctx, img, imageX, imageY, imageWidth, imageHeight, imgWidth, imgHeight);

          // 绘制颜色块
          const colorBlockSize = 215; // 颜色块尺寸
          const colorBlockY = 1820; // 颜色块Y坐标
          const colorCodeY = 2063; // 颜色代码Y坐标

          // 计算色块位置，使第一个色块的左边与照片左边对齐，最后一个色块的右边与照片右边对齐
          const colorBlockXPositions = [];
          const totalBlocks = 5;

          // 使用更精确的方法计算位置
          // 第一个色块的左边缘位置
          const firstBlockX = imageX;

          // 最后一个色块的左边缘位置（使其右边缘与照片右边缘对齐）
          const lastBlockX = imageX + imageWidth - colorBlockSize;

          // 如果只有一个色块
          if (totalBlocks === 1) {
            colorBlockXPositions.push(firstBlockX);
          } else {
            // 计算每个色块之间的步进距离
            const step = (lastBlockX - firstBlockX) / (totalBlocks - 1);

            // 计算每个色块的位置
            for (let i = 0; i < totalBlocks; i++) {
              const x = firstBlockX + i * step;
              colorBlockXPositions.push(x);
            }
          }

          // 验证最后一个色块的右边是否与照片右边对齐
          const lastBlockRightEdge = colorBlockXPositions[totalBlocks - 1] + colorBlockSize;

          this.drawColorBlocks(ctx, colors, colorBlockSize, colorBlockY, colorBlockXPositions, colorCodeY);

          // 保存Canvas
          setTimeout(() => {
            this.saveCanvas(canvas.node);
          }, 300);
        } catch (err) {
          // 绘制失败，触发生成事件
          this.triggerEvent('generated', { path: imagePath });
        }
      } catch (error) {
        // Canvas初始化失败，触发生成事件
        this.triggerEvent('generated', { path: imagePath });
      }
    },

    // 绘制图片 (1:1比例)，带圆角效果
    drawSquareImage(ctx, img, destX, destY, destWidth, destHeight, imgWidth, imgHeight) {
      // 计算裁剪参数，保持1:1比例
      let sourceX = 0;
      let sourceY = 0;
      let sourceWidth = imgWidth;
      let sourceHeight = imgHeight;

      // 计算目标区域的实际比例
      const destRatio = destWidth / destHeight;

      // 根据原始图片比例进行裁剪
      const imgRatio = imgWidth / imgHeight;

      if (imgRatio > destRatio) {
        // 图片比目标区域更宽，需要裁剪左右
        sourceWidth = imgHeight * destRatio;
        sourceX = (imgWidth - sourceWidth) / 2;
      } else if (imgRatio < destRatio) {
        // 图片比目标区域更高，需要裁剪上下
        sourceHeight = imgWidth / destRatio;
        sourceY = (imgHeight - sourceHeight) / 2;
      }

      // 设置圆角半径 - 明显的圆角效果
      const radius = 80; // 圆角半径，根据需求增大

      // 创建圆角矩形路径
      ctx.save(); // 保存当前状态
      this.createRoundedRectPath(ctx, destX, destY, destWidth, destHeight, radius);
      ctx.clip(); // 裁剪为圆角矩形

      // 绘制图片
      ctx.drawImage(
        img,
        sourceX, sourceY, sourceWidth, sourceHeight, // 源图像参数
        destX, destY, destWidth, destHeight // 目标区域参数
      );

      ctx.restore(); // 恢复状态，移除裁剪
    },

    // 创建圆角矩形路径 - 使用arcTo方法确保更精确的圆角
    createRoundedRectPath(ctx, x, y, width, height, radius) {
      // 确保半径不超过宽度和高度的一半
      const r = Math.min(radius, Math.min(width, height) / 2);

      ctx.beginPath();
      // 从左上角开始，顺时针绘制
      ctx.moveTo(x + r, y); // 左上角圆弧的起点
      ctx.lineTo(x + width - r, y); // 上边
      ctx.arcTo(x + width, y, x + width, y + r, r); // 右上角圆弧
      ctx.lineTo(x + width, y + height - r); // 右边
      ctx.arcTo(x + width, y + height, x + width - r, y + height, r); // 右下角圆弧
      ctx.lineTo(x + r, y + height); // 下边
      ctx.arcTo(x, y + height, x, y + height - r, r); // 左下角圆弧
      ctx.lineTo(x, y + r); // 左边
      ctx.arcTo(x, y, x + r, y, r); // 左上角圆弧
      ctx.closePath();
    },

    // 绘制颜色块
    drawColorBlocks(ctx, colors, blockSize, blockY, xPositions, codeY) {
      // 确保最多显示5个颜色
      const actualColors = colors.slice(0, 5);

      // 如果颜色不足5个，补充默认颜色
      while (actualColors.length < 5) {
        const defaultColors = ['#4A4130', '#5D2317', '#900407', '#D8DDE1', '#7E96B2'];
        const missingCount = 5 - actualColors.length;
        for (let i = 0; i < missingCount; i++) {
          actualColors.push(defaultColors[i % defaultColors.length]);
        }
      }

      // 绘制每个颜色块
      actualColors.forEach((color, index) => {
        const x = xPositions[index];
        const y = blockY;

        // 绘制圆角矩形颜色块
        this.drawRoundedRect(ctx, x, y, blockSize, blockSize, 36, color);

        // 获取RGB值
        const rgb = colorUtils.hexToRgb(color);

        // 获取适合背景色的文字颜色 - 强制使用黑色
        const textColors = colorUtils.getTextColorsForBackground(color, true);

        // 绘制颜色代码 - 使用适合背景的颜色
        ctx.fillStyle = textColors.hexTextColor;
        ctx.font = 'bold 36px Arial, sans-serif';
        ctx.textAlign = 'center';

        // 使用格式：# 4A4130
        const colorCode = color.toUpperCase().replace('#', '');

        // 手动实现字母间距
        const text = `# ${colorCode}`;
        const letterSpacing = 1.4; // 字母间距，单位为像素
        let totalWidth = 0;

        // 计算文本总宽度（包含间距）
        for (let i = 0; i < text.length; i++) {
          const metrics = ctx.measureText(text[i]);
          totalWidth += metrics.width + (i < text.length - 1 ? letterSpacing : 0);
        }

        // 计算起始位置，使文本居中
        const startX = x + blockSize / 2 - totalWidth / 2;

        // 逐个字符绘制，实现字母间距效果
        let currentX = startX;
        for (let i = 0; i < text.length; i++) {
          const char = text[i];
          const metrics = ctx.measureText(char);
          ctx.fillText(char, currentX + metrics.width / 2, codeY + 24);
          currentX += metrics.width + letterSpacing;
        }

        // 绘制RGB值 - 使用80%透明度的颜色
        ctx.fillStyle = textColors.rgbTextColor;
        ctx.font = '24px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(`R${rgb.r} G${rgb.g} B${rgb.b}`, x + blockSize/2, codeY + 60);
      });
    },

    // 绘制圆角矩形 - 使用与createRoundedRectPath相同的arcTo方法
    drawRoundedRect(ctx, x, y, width, height, radius, fillColor) {
      // 确保半径不超过宽度和高度的一半
      const r = Math.min(radius, Math.min(width, height) / 2);

      ctx.beginPath();
      // 从左上角开始，顺时针绘制
      ctx.moveTo(x + r, y); // 左上角圆弧的起点
      ctx.lineTo(x + width - r, y); // 上边
      ctx.arcTo(x + width, y, x + width, y + r, r); // 右上角圆弧
      ctx.lineTo(x + width, y + height - r); // 右边
      ctx.arcTo(x + width, y + height, x + width - r, y + height, r); // 右下角圆弧
      ctx.lineTo(x + r, y + height); // 下边
      ctx.arcTo(x, y + height, x, y + height - r, r); // 左下角圆弧
      ctx.lineTo(x, y + r); // 左边
      ctx.arcTo(x, y, x + r, y, r); // 左上角圆弧
      ctx.closePath();

      ctx.fillStyle = fillColor;
      ctx.fill();
    },

    // 保存Canvas
    async saveCanvas(canvasNode) {
      try {
        const { canvasWidth, canvasHeight } = this.data;

        const res = await new Promise((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: canvasNode,
            width: canvasWidth,
            height: canvasHeight,
            destWidth: canvasWidth,
            destHeight: canvasHeight,
            fileType: 'png', // 使用PNG格式以保留透明度和圆角效果
            quality: 1,
            success: resolve,
            fail: reject
          }, this);
        });

        // Canvas保存成功
        this.triggerEvent('generated', { path: res.tempFilePath });
      } catch (err) {
        // Canvas保存失败，使用原始图片路径
        this.triggerEvent('generated', { path: this.properties.imagePath });
      }
    }
  }
})
