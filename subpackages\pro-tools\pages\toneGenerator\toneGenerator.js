// pages/toneGenerator/toneGenerator.js
Page({
  data: {
    // 基础颜色
    baseColor: '#3366FF',
    // 色调数量
    toneCount: 9,
    // 色调列表
    tones: [],
    // 颜色选择器相关
    showColorPicker: false,
    customColor: '#3366FF',
    // 文字颜色（黑色或白色，根据背景色自动计算）
    textColor: '#FFFFFF'
  },

  onLoad: function() {
    // 初始化生成色调
    this.generateTones();
    // 计算文字颜色
    this.calculateTextColor();
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '色调生成器'
    });
  },

  // 生成色调
  generateTones: function() {
    const baseColor = this.data.baseColor;
    const toneCount = this.data.toneCount;
    const tones = [];

    // 解析基础颜色的RGB值
    const r = parseInt(baseColor.substring(1, 3), 16);
    const g = parseInt(baseColor.substring(3, 5), 16);
    const b = parseInt(baseColor.substring(5, 7), 16);

    // 生成不同亮度的色调
    for (let i = 1; i <= toneCount; i++) {
      // 计算亮度因子 (从浅到深)
      const factor = i / (toneCount + 1);

      // 计算新的RGB值 (从浅到深)
      let newR, newG, newB;

      if (i <= Math.floor(toneCount / 2)) {
        // 浅色调 (混合白色)
        const lightFactor = 1 - (factor * 2);
        newR = Math.round(r + (255 - r) * lightFactor);
        newG = Math.round(g + (255 - g) * lightFactor);
        newB = Math.round(b + (255 - b) * lightFactor);
      } else {
        // 深色调 (混合黑色)
        const darkFactor = (factor - 0.5) * 2;
        newR = Math.round(r * (1 - darkFactor));
        newG = Math.round(g * (1 - darkFactor));
        newB = Math.round(b * (1 - darkFactor));
      }

      // 转换回十六进制
      const hexColor = '#' +
        this.componentToHex(newR) +
        this.componentToHex(newG) +
        this.componentToHex(newB);

      // 计算色调级别 (100-900)
      const level = i * 100;

      // 计算文字颜色 (黑色或白色，根据背景色亮度)
      const brightness = (newR * 299 + newG * 587 + newB * 114) / 1000;
      const textColor = brightness > 128 ? '#000000' : '#FFFFFF';

      tones.push({
        level: level,
        color: hexColor,
        textColor: textColor
      });
    }

    this.setData({
      tones: tones
    });
  },

  // 将RGB分量转换为十六进制
  componentToHex: function(c) {
    const hex = c.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  },

  // 显示颜色选择器
  showColorPicker: function() {
    this.setData({
      showColorPicker: true,
      customColor: this.data.baseColor
    });
  },

  // 隐藏颜色选择器
  hideColorPicker: function() {
    this.setData({
      showColorPicker: false
    });
  },

  // 颜色选择器变化事件
  onColorPickerChange: function(e) {
    const color = e.detail.color;
    this.setData({
      customColor: color
    });
  },

  // 颜色选择器确认事件
  onColorPickerConfirm: function(e) {
    const color = e.detail.color;
    this.setData({
      baseColor: color,
      showColorPicker: false
    });

    // 重新生成色调
    this.generateTones();
    // 计算文字颜色
    this.calculateTextColor();
  },

  // 生成随机颜色
  generateRandomColor: function() {
    // 生成随机的RGB值
    const r = Math.floor(Math.random() * 256);
    const g = Math.floor(Math.random() * 256);
    const b = Math.floor(Math.random() * 256);

    // 转换为十六进制
    const randomColor = '#' +
      this.componentToHex(r) +
      this.componentToHex(g) +
      this.componentToHex(b);

    this.setData({
      baseColor: randomColor
    });

    // 重新生成色调
    this.generateTones();
    // 计算文字颜色
    this.calculateTextColor();
  },

  // 计算文字颜色（黑色或白色，根据背景色亮度）
  calculateTextColor: function() {
    const color = this.data.baseColor;

    // 解析RGB值
    const r = parseInt(color.substring(1, 3), 16);
    const g = parseInt(color.substring(3, 5), 16);
    const b = parseInt(color.substring(5, 7), 16);

    // 计算亮度 (YIQ公式)
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    // 如果亮度大于128，使用黑色文字，否则使用白色
    const textColor = brightness > 128 ? '#000000' : '#FFFFFF';

    this.setData({
      textColor: textColor
    });
  },

  // 复制颜色代码
  copyColor: function(e) {
    const color = e.currentTarget.dataset.color;

    wx.setClipboardData({
      data: color,
      success: () => {
        // 不显示吐司提示，直接复制到剪贴板
      }
    });
  }
});
