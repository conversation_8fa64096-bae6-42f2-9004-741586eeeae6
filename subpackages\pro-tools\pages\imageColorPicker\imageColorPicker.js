// pages/imageColorPicker/imageColorPicker.js

Page({
  /**
   * 页面的初始数据
   */
  data: {
    imagePath: '', // 图片路径
    imageInfo: null, // 图片信息
    pickedColor: '', // 取色结果 (HEX)
    pickedRgb: '', // 取色结果 (RGB)
    initialColor: '', // 初始颜色
    fromColorPicker: false, // 是否从颜色选择器组件进入

    // 页面状态相关
    isLoading: false, // 是否正在加载
    hasError: false, // 是否发生错误
    errorMessage: '', // 错误信息

    // 图片缩放相关
    scale: 1, // 当前缩放比例
    minScale: 1, // 最小缩放比例
    maxScale: 12, // 最大缩放比例
    lastDistance: 0, // 上次双指间距离
    isScaling: false, // 是否正在缩放

    // 图片平移相关
    offsetX: 0, // 图片X轴偏移量
    offsetY: 0, // 图片Y轴偏移量
    lastTouchX: 0, // 上次触摸X坐标
    lastTouchY: 0, // 上次触摸Y坐标
    isPanning: false, // 是否正在平移
    showPanningTip: false, // 是否显示平移提示

    // 放大镜缩略图相关
    magnifierZoom: 4, // 放大镜缩放倍数

    // 双击居中相关
    lastTapTime: 0 // 上次点击时间
  },

  // 调试模式标志，控制日志输出
  DEBUG: false,

  // 上次节流函数调用时间
  lastThrottleTime: 0,

  /**
   * 日志输出函数，只在调试模式下输出
   */
  log: function(message, data) {
    if (this.DEBUG) {
      console.log(message, data);
    }
  },

  /**
   * 节流函数，限制函数调用的频率
   * @param {Function} fn 要执行的函数
   * @param {Number} delay 延迟时间，默认100ms
   * @returns {Function} 节流后的函数
   */
  throttle: function(fn, delay = 100) {
    const now = Date.now();
    if (now - this.lastThrottleTime > delay) {
      fn();
      this.lastThrottleTime = now;
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    // 设置加载状态
    this.setData({ isLoading: true, hasError: false, errorMessage: '' });

    try {
      // 获取传递的初始颜色
      if (options.color) {
        const initialColor = decodeURIComponent(options.color);
        this.setData({
          initialColor: initialColor,
          pickedColor: initialColor,
          pickedRgb: this.hexToRgb(initialColor)
        });
      }

      // 获取传递的图片路径
      if (options.imagePath) {
        const imagePath = decodeURIComponent(options.imagePath);
        this.loadImage(imagePath);
      }

      // 获取事件通道
      this.eventChannel = this.getOpenerEventChannel();

      // 判断来源 - 从颜色选择器组件进入还是从首页进入
      // 检查事件通道是否有效，以及是否有监听acceptColorFromImagePicker事件
      try {
        const hasEventChannel = this.eventChannel && typeof this.eventChannel.emit === 'function';

        // 更可靠的检测方法：检查是否有color参数，颜色选择器组件会传递color参数
        const isFromColorPicker = !!(hasEventChannel && options.color);

        this.setData({
          fromColorPicker: isFromColorPicker
        });

        this.log('来源检测:', {
          hasEventChannel,
          hasColorParam: !!options.color,
          isFromColorPicker
        });
      } catch (err) {
        this.log('检测来源出错:', err);
        this.setData({
          fromColorPicker: false // 出错时默认为不是从颜色选择器进入
        });
      }

      // 如果没有传入图片路径，则结束加载状态
      if (!options.imagePath) {
        this.setData({ isLoading: false });
      }
    } catch (error) {
      this.log('页面加载错误:', error);
      this.handleError('页面加载失败，请重试');
    }
  },

  /**
   * 处理错误
   */
  handleError: function(message) {
    this.setData({
      isLoading: false,
      hasError: true,
      errorMessage: message || '发生错误，请重试'
    });
    this.log('图片取色器错误:', message);

    // 显示错误提示
    wx.showToast({
      title: message || '发生错误，请重试',
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 重新加载页面
   */
  reloadPage: function() {
    this.setData({
      isLoading: false,
      hasError: false,
      errorMessage: '',
      imagePath: '',
      imageInfo: null,
      pickedColor: this.data.initialColor || '',
      pickedRgb: this.data.initialColor ? this.hexToRgb(this.data.initialColor) : '',
      scale: 1,
      offsetX: 0,
      offsetY: 0
    });

    // 重新初始化Canvas
    setTimeout(() => {
      this.initCanvases();
    }, 300);
  },

  /**
   * 加载图片
   */
  loadImage: function(imagePath) {
    // 设置加载状态
    this.setData({ isLoading: true, hasError: false });

    try {
      wx.getImageInfo({
        src: imagePath,
        success: (imageInfo) => {
          this.setData({
            imagePath: imagePath,
            imageInfo: imageInfo,
            isLoading: false
          });

          // 延迟获取图片区域尺寸信息
          setTimeout(() => {
            try {
              this.getImageRect();
              this.initializeColorPicker(imageInfo);
            } catch (error) {
              this.log('初始化取色器失败:', error);
              this.handleError('初始化取色器失败，请重试');
            }
          }, 100);
        },
        fail: (err) => {
          this.log('获取图片信息失败:', err);
          this.handleError('图片加载失败，请重试');
        }
      });
    } catch (error) {
      this.log('加载图片过程出错:', error);
      this.handleError('加载图片失败，请重试');
    }
  },

  /**
   * 初始化取色器
   */
  initializeColorPicker: function(imageInfo) {
    // 初始化放大镜缩略图，默认显示图片中心位置
    setTimeout(() => {
      const centerX = imageInfo.width / 2;
      const centerY = imageInfo.height / 2;
      this.updateMagnifier(centerX, centerY);
      this.getColorFromImage(centerX, centerY);
    }, 300);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '图片取色'
    });

    // 初始化canvas
    this.initCanvases();
  },

  /**
   * 初始化canvas - 使用Canvas 2D API
   */
  initCanvases: function() {
    let retryCount = this.canvasRetryCount || 0;

    try {
      // 初始化离屏Canvas - 用于取色
      this.initOffscreenCanvas();

      // 初始化放大镜Canvas
      this.initMagnifierCanvas();

      this.log('Canvas 2D初始化成功');

      // 重置重试计数
      this.canvasRetryCount = 0;
    } catch (error) {
      this.log('Canvas 2D初始化失败:', error);

      // 增加重试计数
      retryCount++;
      this.canvasRetryCount = retryCount;

      if (retryCount < 3) {
        // 延迟重试，最多重试3次
        setTimeout(() => this.initCanvases(), 300);
      } else {
        // 超过重试次数，显示错误
        this.handleError('画布初始化失败，请重新进入页面');
      }
    }
  },

  /**
   * 初始化离屏Canvas
   */
  initOffscreenCanvas: function() {
    const query = wx.createSelectorQuery();
    query.select('#offscreenCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res && res[0]) {
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');

          // 设置Canvas尺寸
          let dpr = 2; // 默认值
          try {
            const deviceInfo = wx.getDeviceInfo();
            dpr = deviceInfo.pixelRatio || 2;
          } catch (error) {
            console.warn('获取设备信息失败，使用默认pixelRatio:', error);
          }
          canvas.width = 300 * dpr;
          canvas.height = 300 * dpr;
          ctx.scale(dpr, dpr);

          // 初始化背景
          ctx.fillStyle = '#FFFFFF';
          ctx.fillRect(0, 0, 300, 300);

          this.offscreenCanvas = canvas;
          this.offscreenCtx = ctx;

          // 注册Canvas上下文到生命周期管理器
          if (this.registerCanvasContext) {
            this.registerCanvasContext(ctx);
          }

          this.log('离屏Canvas 2D初始化成功');
        }
      });
  },

  /**
   * 初始化放大镜Canvas
   */
  initMagnifierCanvas: function() {
    const query = wx.createSelectorQuery();
    query.select('#magnifierCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res && res[0]) {
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');

          // 设置Canvas尺寸
          let dpr = 2; // 默认值
          try {
            const deviceInfo = wx.getDeviceInfo();
            dpr = deviceInfo.pixelRatio || 2;
          } catch (error) {
            console.warn('获取设备信息失败，使用默认pixelRatio:', error);
          }
          canvas.width = res[0].width * dpr;
          canvas.height = res[0].height * dpr;
          ctx.scale(dpr, dpr);

          this.magnifierCanvas = canvas;
          this.magnifierCtx = ctx;

          // 注册Canvas上下文到生命周期管理器
          if (this.registerCanvasContext) {
            this.registerCanvasContext(ctx);
          }

          this.log('放大镜Canvas 2D初始化成功');
        }
      });
  },

  /**
   * 获取离屏Canvas上下文
   */
  getOffscreenContext: function() {
    if (!this.offscreenCtx) {
      this.initOffscreenCanvas();
    }
    return this.offscreenCtx;
  },

  /**
   * 获取放大镜Canvas上下文
   */
  getMagnifierContext: function() {
    if (!this.magnifierCtx) {
      this.initMagnifierCanvas();
    }
    return this.magnifierCtx;
  },

  /**
   * 选择图片
   */
  chooseImage: function() {
    try {
      wx.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          if (res.tempFilePaths && res.tempFilePaths.length > 0) {
            this.loadImage(res.tempFilePaths[0]);
          } else {
            this.log('未获取到图片路径');
            this.setData({ isLoading: false });
          }
        },
        fail: (err) => {
          this.log('选择图片失败:', err);
          // 用户取消选择图片不显示错误
          if (err.errMsg !== 'chooseImage:fail cancel') {
            this.handleError('选择图片失败，请重试');
          }
          this.setData({ isLoading: false });
        }
      });
    } catch (error) {
      this.log('选择图片过程出错:', error);
      this.handleError('选择图片失败，请重试');
    }
  },

  /**
   * 图片加载完成事件
   */
  onImageLoad: function() {
    // 延迟获取图片区域尺寸信息
    setTimeout(() => {
      this.getImageRect();

      // 如果已有图片信息，直接初始化取色器
      if (this.data.imageInfo) {
        this.initializeColorPicker(this.data.imageInfo);
      } else {
        // 否则先获取图片信息
        wx.getImageInfo({
          src: this.data.imagePath,
          success: (res) => {
            this.setData({ imageInfo: res });
            this.initializeColorPicker(res);
          }
        });
      }
    }, 100);
  },

  /**
   * 获取图片区域尺寸信息
   */
  getImageRect: function() {
    const query = wx.createSelectorQuery();
    query.select('#picker-image').boundingClientRect();
    query.exec((res) => {
      if (res && res[0]) {
        this.imageRect = res[0];
        this.log('图片尺寸信息:', res[0]);
      }
    });
  },

  /**
   * 计算图片显示尺寸和偏移量
   * 返回 { displayWidth, displayHeight, baseOffsetX, baseOffsetY }
   */
  calculateImageDisplaySize: function(imageInfo, containerWidth, containerHeight) {
    if (!imageInfo) return null;

    // 计算图片的实际显示尺寸（未缩放前）
    let displayWidth, displayHeight, baseOffsetX, baseOffsetY;

    // 计算宽高比
    const imageRatio = imageInfo.width / imageInfo.height;
    const containerRatio = containerWidth / containerHeight;

    if (imageRatio > containerRatio) {
      // 图片较宽，宽度撑满容器
      displayWidth = containerWidth;
      displayHeight = containerWidth / imageRatio;
      baseOffsetX = 0;
      baseOffsetY = (containerHeight - displayHeight) / 2;
    } else {
      // 图片较高，高度撑满容器
      displayHeight = containerHeight;
      displayWidth = containerHeight * imageRatio;
      baseOffsetX = (containerWidth - displayWidth) / 2;
      baseOffsetY = 0;
    }

    return { displayWidth, displayHeight, baseOffsetX, baseOffsetY };
  },

  /**
   * 图片点击事件 - 用于点击取色和双击居中
   */
  onImageTap: function(e) {
    const currentTime = Date.now();
    const lastTapTime = this.data.lastTapTime;

    // 检测是否为双击（两次点击间隔小于300ms）
    if (currentTime - lastTapTime < 300) {
      // 双击操作 - 将点击位置移动到屏幕中央
      this.centerOnPoint(e);
    } else {
      // 单击操作 - 取色
      const touch = e.changedTouches ? e.changedTouches[0] : e.touches ? e.touches[0] : e;
      this.calculateTouchPosition(touch, true);
    }

    // 更新最后点击时间
    this.setData({
      lastTapTime: currentTime
    });
  },

  /**
   * 将点击位置移动到屏幕中央
   */
  centerOnPoint: function(e) {
    // 只有在图片已放大的情况下才执行居中
    if (this.data.scale <= 1) {
      wx.showToast({
        title: '请先放大图片',
        icon: 'none'
      });
      return;
    }

    const touch = e.changedTouches ? e.changedTouches[0] : e.touches ? e.touches[0] : e;

    // 获取图片和容器信息
    const query = wx.createSelectorQuery();
    query.select('#picker-image').boundingClientRect();
    query.select('.image-container').boundingClientRect();
    query.exec((res) => {
      if (!res || !res[0] || !res[1]) return;

      const imageNode = res[0];
      const containerNode = res[1];
      const containerPadding = parseInt(20);

      // 计算容器中心点
      const containerCenterX = (containerNode.width - containerPadding * 2) / 2;
      const containerCenterY = (containerNode.height - containerPadding * 2) / 2;

      // 计算触摸点相对于容器的位置
      const touchX = touch.pageX - containerNode.left - containerPadding;
      const touchY = touch.pageY - containerNode.top - containerPadding;

      // 计算需要的偏移量，使触摸点移动到容器中心
      const deltaX = containerCenterX - touchX;
      const deltaY = containerCenterY - touchY;

      // 更新偏移量
      this.setData({
        offsetX: this.data.offsetX + deltaX / this.data.scale,
        offsetY: this.data.offsetY + deltaY / this.data.scale
      });

      // 显示提示
      wx.showToast({
        title: '已居中',
        icon: 'none'
      });
    });
  },

  /**
   * 图片触摸开始事件
   */
  onImageTouchStart: function(e) {
    // 单指触摸 - 准备取色
    if (e.touches.length === 1) {
      const touch = e.touches[0];

      // 记录初始触摸位置，用于判断是否为长按或拖动
      this.setData({
        lastTouchX: touch.clientX,
        lastTouchY: touch.clientY,
        isPanning: false,
        touchStartTime: Date.now() // 记录触摸开始时间
      });

      // 立即进行取色，提供即时反馈
      this.calculateTouchPosition(touch, true);

      // 设置长按定时器，长按才进入平移模式
      this.longPressTimer = setTimeout(() => {
        // 只有在缩放比例大于1时才允许平移
        if (this.data.scale > 1) {
          this.setData({
            isPanning: true,
            showPanningTip: true // 显示平移提示
          });
          wx.vibrateShort(); // 震动反馈，表示进入平移模式

          // 3秒后自动隐藏提示
          setTimeout(() => {
            this.setData({
              showPanningTip: false
            });
          }, 3000);
        }
      }, 800); // 800ms长按判定
    }
    // 双指触摸 - 准备缩放
    else if (e.touches.length === 2) {
      // 清除长按定时器
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }

      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );

      this.setData({
        lastDistance: distance,
        isScaling: true,
        isPanning: false
      });
    }
  },

  /**
   * 图片触摸移动事件
   */
  onImageTouchMove: function(e) {
    // 单指移动
    if (e.touches.length === 1) {
      const touch = e.touches[0];

      // 计算移动距离
      const deltaX = touch.clientX - this.data.lastTouchX;
      const deltaY = touch.clientY - this.data.lastTouchY;
      const moveDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // 如果是平移模式（长按后）
      if (this.data.isPanning && this.data.scale > 1) {
        // 获取图片和容器信息，用于计算平移限制
        const query = wx.createSelectorQuery();
        query.select('#picker-image').boundingClientRect();
        query.select('.image-container').boundingClientRect();
        query.exec((res) => {
          if (!res || !res[0] || !res[1]) return;

          const imageNode = res[0];
          const containerNode = res[1];
          const containerPadding = parseInt(20); // 从CSS中获取的padding值（20rpx）

          const containerWidth = containerNode.width - containerPadding * 2;
          const containerHeight = containerNode.height - containerPadding * 2;

          // 计算图片的实际显示尺寸（未缩放前）
          let displayWidth, displayHeight, baseOffsetX, baseOffsetY;

          // 计算宽高比
          const imageRatio = this.data.imageInfo.width / this.data.imageInfo.height;
          const containerRatio = containerWidth / containerHeight;

          if (imageRatio > containerRatio) {
            // 图片较宽，宽度撑满容器
            displayWidth = containerWidth;
            displayHeight = containerWidth / imageRatio;
            baseOffsetX = 0;
            baseOffsetY = (containerHeight - displayHeight) / 2;
          } else {
            // 图片较高，高度撑满容器
            displayHeight = containerHeight;
            displayWidth = containerHeight * imageRatio;
            baseOffsetX = (containerWidth - displayWidth) / 2;
            baseOffsetY = 0;
          }

          // 计算缩放后的尺寸
          const scaledWidth = displayWidth * this.data.scale;
          const scaledHeight = displayHeight * this.data.scale;

          // 计算允许的最大偏移量，确保图片的所有区域都可以被访问到
          // 增加额外的偏移量，允许图片完全移出容器，以便能够选择边缘区域
          const extraOffset = Math.max(displayWidth, displayHeight) * 0.5; // 允许额外偏移50%

          const maxOffsetX = (scaledWidth - displayWidth) / 2 / this.data.scale + extraOffset;
          const maxOffsetY = (scaledHeight - displayHeight) / 2 / this.data.scale + extraOffset;

          // 更新偏移量，考虑缩放因素，但不限制平移范围
          // 这样用户可以将图片的任何部分移动到屏幕中央
          const newOffsetX = this.data.offsetX + deltaX / this.data.scale;
          const newOffsetY = this.data.offsetY + deltaY / this.data.scale;

          this.setData({
            offsetX: newOffsetX,
            offsetY: newOffsetY,
            lastTouchX: touch.clientX,
            lastTouchY: touch.clientY
          });
        });
      }
      // 如果不是平移模式，则进行取色
      // 但只有当移动距离超过阈值时才更新，避免频繁取色
      else {
        // 清除长按定时器，因为用户正在移动
        if (this.longPressTimer) {
          clearTimeout(this.longPressTimer);
          this.longPressTimer = null;
        }

        // 如果移动距离超过阈值，则进行取色
        if (moveDistance > 2) { // 2像素的移动阈值
          // 使用节流函数限制取色频率
          this.throttle(() => {
            this.calculateTouchPosition(touch, true);
          }, 50); // 50ms的节流延迟

          // 更新最后触摸位置
          this.setData({
            lastTouchX: touch.clientX,
            lastTouchY: touch.clientY
          });
        }
      }
    }
    // 双指移动 - 缩放
    else if (e.touches.length === 2) {
      // 清除长按定时器
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }

      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );

      // 计算新的缩放比例
      const scale = Math.min(
        Math.max(
          this.data.scale * (distance / this.data.lastDistance),
          this.data.minScale
        ),
        this.data.maxScale
      );

      // 计算缩放中心点（两个触摸点的中心）
      const centerX = (touch1.clientX + touch2.clientX) / 2;
      const centerY = (touch1.clientY + touch2.clientY) / 2;

      this.setData({
        scale: scale,
        lastDistance: distance,
        isScaling: true
      });
    }
  },

  /**
   * 图片触摸结束事件
   */
  onImageTouchEnd: function(e) {
    // 清除长按定时器
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    // 如果是缩放模式，结束缩放
    if (this.data.isScaling) {
      this.setData({
        isScaling: false
      });

      // 如果缩放比例接近1，自动重置为1并居中
      if (this.data.scale < 1.05 && this.data.scale > 0.95) {
        // 自动重置为默认状态，不显示提示
        this.resetScale();
      } else {
        // 缩放结束后，如果有触摸点，进行取色
        if (e.changedTouches && e.changedTouches.length > 0) {
          const touch = e.changedTouches[0];
          // 延迟一下取色，确保UI已更新
          setTimeout(() => {
            this.calculateTouchPosition(touch, true);
          }, 50);
        }
      }
      return;
    }

    // 如果是平移模式，结束平移
    if (this.data.isPanning) {
      this.setData({
        isPanning: false
      });

      // 平移结束后，如果有触摸点，进行取色
      if (e.changedTouches && e.changedTouches.length > 0) {
        const touch = e.changedTouches[0];
        // 延迟一下取色，确保UI已更新
        setTimeout(() => {
          this.calculateTouchPosition(touch, true);
        }, 50);
      }
      return;
    }

    // 如果既不是缩放也不是平移，则进行取色
    if (e.changedTouches && e.changedTouches.length > 0) {
      const touch = e.changedTouches[0];
      this.calculateTouchPosition(touch, true);
    }
  },

  /**
   * 阻止页面滚动
   */
  onCatchTouchMove: function() {
    return false;
  },

  /**
   * 重置缩放
   */
  resetScale: function() {
    this.setData({
      scale: 1,
      offsetX: 0,
      offsetY: 0
    });
  },

  // 小地图功能已移除

  /**
   * 计算触摸位置并更新放大镜
   */
  calculateTouchPosition: function(touch, shouldPickColor = true) {
    const { imageInfo, scale, offsetX, offsetY } = this.data;
    if (!imageInfo) {
      console.log('缺少图片信息');
      return;
    }

    try {
      // 获取图片节点信息和容器信息
      const query = wx.createSelectorQuery();
      query.select('#picker-image').boundingClientRect();
      query.select('.image-container').boundingClientRect();
      query.exec((res) => {
        if (!res || !res[0] || !res[1]) {
          console.error('获取图片或容器节点信息失败');
          return;
        }

        const imageNode = res[0];
        const containerNode = res[1];

        // 计算容器的内边距
        const containerPadding = parseInt(20); // 从CSS中获取的padding值（20rpx）

        // 计算触摸点相对于容器的位置（考虑内边距）
        const touchX = touch.pageX - containerNode.left - containerPadding;
        const touchY = touch.pageY - containerNode.top - containerPadding;

        // 记录触摸点位置
        this.log('触摸点位置:', {
          touchX, touchY,
          containerWidth: containerNode.width - containerPadding * 2,
          containerHeight: containerNode.height - containerPadding * 2,
          scale: scale,
          offsetX: offsetX,
          offsetY: offsetY
        });

        // 计算图片的实际显示尺寸和偏移量（考虑内边距）
        const { width: originalWidth, height: originalHeight } = imageInfo;
        const containerWidth = containerNode.width - containerPadding * 2;
        const containerHeight = containerNode.height - containerPadding * 2;

        // 使用公共函数计算图片显示尺寸和偏移量
        const { displayWidth, displayHeight, baseOffsetX, baseOffsetY } =
          this.calculateImageDisplaySize(imageInfo, containerWidth, containerHeight);

        // 考虑缩放和平移的影响
        // 缩放后的尺寸
        const scaledWidth = displayWidth * scale;
        const scaledHeight = displayHeight * scale;

        // 计算缩放中心点（容器中心）
        const containerCenterX = containerWidth / 2;
        const containerCenterY = containerHeight / 2;

        // 计算缩放和平移后的偏移量 - 使用更精确的计算方法
        // 关键修复：修正缩放中心点的计算，避免偏移
        const scaleFactor = scale - 1; // 缩放因子，表示相对于原始大小增加的比例
        const totalOffsetX = baseOffsetX + offsetX * scale;
        const totalOffsetY = baseOffsetY + offsetY * scale;

        this.log('偏移量计算:', {
          baseOffsetX, baseOffsetY,
          offsetX, offsetY,
          scale,
          containerCenterX, containerCenterY,
          totalOffsetX, totalOffsetY
        });

        // 计算触摸点在缩放和平移后的图片坐标系中的位置
        // 关键修复：使用更精确的坐标转换方法

        // 1. 计算触摸点相对于容器中心的偏移
        const touchOffsetX = touchX - containerCenterX;
        const touchOffsetY = touchY - containerCenterY;

        // 2. 计算图片中心点在容器中的位置（考虑平移）
        const imageCenterX = containerCenterX + offsetX * scale;
        const imageCenterY = containerCenterY + offsetY * scale;

        // 3. 计算触摸点相对于图片中心的偏移
        const relativeToImageCenterX = touchX - imageCenterX;
        const relativeToImageCenterY = touchY - imageCenterY;

        // 4. 将触摸点坐标转换为相对于缩放和平移后的图片左上角的坐标
        const relativeToScaledX = touchX - totalOffsetX;
        const relativeToScaledY = touchY - totalOffsetY;

        // 5. 将这个坐标转换回未缩放的图片坐标系
        const relativeX = relativeToScaledX / scale;
        const relativeY = relativeToScaledY / scale;

        // 允许相对坐标超出图片实际显示区域，以支持边缘取色
        // 不限制边界溢出，允许用户选择图片的任何区域
        // 这样即使图片放大后，四周的区域也能被选择取色

        // 将相对位置转换为图片原始尺寸上的坐标
        // 使用线性映射，将显示坐标映射到原始图片坐标
        let x, y;

        // 关键修复：使用更精确的坐标映射方法
        // 计算图片中心在原始图片坐标系中的位置
        const originalCenterX = originalWidth / 2;
        const originalCenterY = originalHeight / 2;

        // 计算触摸点相对于图片中心的比例
        const relativeRatioX = relativeToImageCenterX / (displayWidth * scale / 2);
        const relativeRatioY = relativeToImageCenterY / (displayHeight * scale / 2);

        // 使用比例计算在原始图片上的坐标
        let mappedX = originalCenterX + relativeRatioX * originalWidth / 2;
        let mappedY = originalCenterY + relativeRatioY * originalHeight / 2;

        // 处理边缘情况，确保坐标计算准确
        if (mappedX < 0) {
          // 左边缘
          x = 0;
        } else if (mappedX >= originalWidth) {
          // 右边缘
          x = originalWidth - 1;
        } else {
          // 正常范围内 - 使用映射坐标
          x = mappedX;
        }

        if (mappedY < 0) {
          // 上边缘
          y = 0;
        } else if (mappedY >= originalHeight) {
          // 下边缘
          y = originalHeight - 1;
        } else {
          // 正常范围内 - 使用映射坐标
          y = mappedY;
        }

        // 确保坐标是整数，避免小数点导致的精度问题
        x = Math.round(x);
        y = Math.round(y);

        this.log('计算后的图片坐标:', {
          x, y,
          originalWidth, originalHeight,
          relativeX, relativeY,
          touchOffsetX, touchOffsetY,
          imageCenterX, imageCenterY,
          relativeToImageCenterX, relativeToImageCenterY,
          scale, offsetX, offsetY
        });

        // 更新放大镜
        this.updateMagnifier(x, y);

        // 如果需要取色，则获取颜色
        if (shouldPickColor) {
          this.getColorFromImage(x, y);
        }
      });
    } catch (error) {
      console.error('计算触摸位置失败:', error);
      // 显示错误提示
      wx.showToast({
        title: '取色失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 更新放大镜
   */
  updateMagnifier: function(x, y) {
    const { imageInfo, scale } = this.data;
    if (!imageInfo) return;

    try {
      // 获取放大镜Canvas上下文
      const ctx = this.getMagnifierContext();

      // 获取放大镜Canvas的尺寸和图片容器尺寸
      const query = wx.createSelectorQuery();
      query.select('.magnifier-canvas').boundingClientRect();
      query.select('#picker-image').boundingClientRect();
      query.select('.image-container').boundingClientRect();
      query.exec((res) => {
        if (!res || !res[0] || !res[1] || !res[2]) return;

        const magnifierSize = res[0].width;
        const radius = magnifierSize / 2;
        const imageNode = res[1];
        const containerNode = res[2];

        // 清空Canvas
        ctx.clearRect(0, 0, magnifierSize, magnifierSize);

        // 绘制圆形裁剪区域
        ctx.save();
        ctx.beginPath();
        ctx.arc(radius, radius, radius, 0, Math.PI * 2);
        ctx.clip();

        // 计算图片在容器中的实际显示比例
        // 这是图片适应容器时的初始缩放比例
        const containerPadding = 20; // 容器的padding
        const containerWidth = containerNode.width - containerPadding * 2;
        const containerHeight = containerNode.height - containerPadding * 2;

        // 使用公共函数计算图片显示尺寸
        const { displayWidth, displayHeight } =
          this.calculateImageDisplaySize(imageInfo, containerWidth, containerHeight);

        // 计算图片的实际显示比例（相对于原始尺寸）
        const initialScale = displayWidth / imageInfo.width;

        // 计算当前的综合缩放比例（初始缩放 * 用户缩放）
        const effectiveScale = initialScale * scale;

        this.log('图片缩放信息:', {
          initialScale,
          userScale: scale,
          effectiveScale,
          imageSize: { width: imageInfo.width, height: imageInfo.height },
          displaySize: { width: displayWidth, height: displayHeight },
          containerSize: { width: containerWidth, height: containerHeight }
        });

        // 根据综合缩放比例决定放大镜的行为
        // 放大镜显示比图片当前显示比例放大1.5倍的内容
        const magnifierScale = effectiveScale * 1.5; // 放大镜比图片综合放大倍数再放大1.5倍
        const sourceSize = magnifierSize / magnifierScale;
        const sourceX = x - sourceSize / 2;
        const sourceY = y - sourceSize / 2;

        this.log('放大镜参数:', {
          x, y,
          sourceX, sourceY,
          sourceSize,
          magnifierSize,
          effectiveScale,
          magnifierScale, // 添加放大镜实际缩放比例
          magnificationRatio: magnifierScale / effectiveScale // 放大镜相对于图片的放大倍数
        });

        // 创建图片对象用于Canvas 2D API
        const img = this.magnifierCanvas.createImage();
        img.onload = () => {
          // 绘制图片到放大镜
          ctx.drawImage(
            img,
            sourceX,
            sourceY,
            sourceSize,
            sourceSize,
            0,
            0,
            magnifierSize,
            magnifierSize
          );

          // 绘制十字线
          ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
          ctx.lineWidth = 1;
          ctx.beginPath();
          ctx.moveTo(radius, 0);
          ctx.lineTo(radius, magnifierSize);
          ctx.moveTo(0, radius);
          ctx.lineTo(magnifierSize, radius);
          ctx.stroke();

          // 绘制中心点
          ctx.beginPath();
          ctx.arc(radius, radius, 2, 0, Math.PI * 2);
          ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
          ctx.fill();

          // 恢复上下文
          ctx.restore();

          // 绘制边框
          ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
          ctx.lineWidth = 1;
          ctx.beginPath();
          ctx.arc(radius, radius, radius, 0, Math.PI * 2);
          ctx.stroke();
        };
        img.onerror = () => {
          this.log('放大镜图片加载失败');
        };
        img.src = imageInfo.path;
      });
    } catch (error) {
      // 更新放大镜失败，静默处理
      // 不显示错误提示，避免频繁弹窗影响用户体验
      this.log('放大镜更新错误:', error);
    }
  },

  /**
   * 从图片获取颜色
   */
  getColorFromImage: function(x, y) {
    const { imagePath, imageInfo } = this.data;
    if (!imagePath || !imageInfo) {
      console.error('缺少图片信息');
      return;
    }

    try {
      // 检查坐标是否在图片范围内
      const isOutOfBounds = x < 0 || y < 0 || x >= imageInfo.width || y >= imageInfo.height;

      // 如果坐标超出图片范围，使用最近的边缘像素
      const safeX = Math.max(0, Math.min(x, imageInfo.width - 1));
      const safeY = Math.max(0, Math.min(y, imageInfo.height - 1));

      this.log('取色坐标:', {
        originalX: x,
        originalY: y,
        safeX: safeX,
        safeY: safeY,
        imageWidth: imageInfo.width,
        imageHeight: imageInfo.height,
        isOutOfBounds: isOutOfBounds
      });

      // 如果坐标超出图片范围，直接使用边缘像素
      if (isOutOfBounds) {
        this.getEdgePixelColor(safeX, safeY);
        return;
      }

      // 在离屏Canvas上绘制图片的指定区域
      const ctx = this.getOffscreenContext();
      if (!ctx) {
        console.error('无法获取Canvas上下文');
        this.useFixedEdgeColor(x, y);
        return;
      }

      // 清空Canvas
      ctx.clearRect(0, 0, 10, 10);

      // 计算源区域，确保不会超出图片边界
      let sourceX = safeX - 1;
      let sourceY = safeY - 1;
      let sourceWidth = 3;
      let sourceHeight = 3;
      let destX = 0;
      let destY = 0;

      // 处理左边界
      if (sourceX < 0) {
        destX = Math.abs(sourceX);
        sourceWidth -= destX;
        sourceX = 0;
      }

      // 处理上边界
      if (sourceY < 0) {
        destY = Math.abs(sourceY);
        sourceHeight -= destY;
        sourceY = 0;
      }

      // 处理右边界
      if (sourceX + sourceWidth > imageInfo.width) {
        sourceWidth = imageInfo.width - sourceX;
      }

      // 处理下边界
      if (sourceY + sourceHeight > imageInfo.height) {
        sourceHeight = imageInfo.height - sourceY;
      }

      // 创建图片对象用于Canvas 2D API
      const img = this.offscreenCanvas.createImage();
      img.onload = () => {
        // 绘制图片的指定区域
        if (sourceWidth > 0 && sourceHeight > 0) {
          try {
            ctx.drawImage(
              img,
              sourceX,
              sourceY,
              sourceWidth,
              sourceHeight,
              destX,
              destY,
              sourceWidth,
              sourceHeight
            );

            // 获取像素数据 - 直接获取中心点的颜色
            try {
              const imageData = ctx.getImageData(1, 1, 1, 1);
              const data = imageData.data;

              // 提取RGB值
              const r = data[0];
              const g = data[1];
              const b = data[2];

              // 转换为HEX格式
              const hexColor = '#' + this.rgbToHex(r, g, b);
              const rgbColor = `rgb(${r},${g},${b})`;

              // 更新颜色值
              this.setData({
                pickedColor: hexColor,
                pickedRgb: rgbColor
              });

              this.log('取色成功:', { hexColor, rgbColor });
            } catch (error) {
              console.error('获取像素数据失败:', error);
              this.getEdgePixelColor(safeX, safeY);
            }
          } catch (err) {
            console.error('绘制图片区域失败:', err);
            this.useFixedEdgeColor(x, y);
          }
        } else {
          console.error('无效的源区域尺寸');
          this.useFixedEdgeColor(x, y);
        }
      };
      img.onerror = () => {
        console.error('图片加载失败');
        this.useFixedEdgeColor(x, y);
      };
      img.src = imagePath;
    } catch (error) {
      // 取色失败，使用备用方案
      this.useFixedEdgeColor(x, y);

      // 显示轻量级错误提示
      wx.showToast({
        title: '取色遇到问题，已使用备用颜色',
        icon: 'none',
        duration: 1500
      });
    }
  },

  /**
   * 获取边缘像素颜色 - 备用方法
   * 优化版本，使用小尺寸Canvas，只绘制需要的区域
   */
  getEdgePixelColor: function(x, y) {
    const { imagePath, imageInfo } = this.data;
    if (!imagePath || !imageInfo) return;

    try {
      // 在离屏Canvas上绘制图片的边缘区域
      const ctx = this.getOffscreenContext();

      // 清空Canvas
      ctx.clearRect(0, 0, 10, 10);

      // 计算源区域 - 只绘制边缘附近的小区域
      // 使用5x5的区域，确保有足够的像素可以取色
      const size = 5;
      const halfSize = Math.floor(size / 2);

      // 计算源区域的左上角坐标，确保不会超出图片边界
      const sourceX = Math.max(0, x - halfSize);
      const sourceY = Math.max(0, y - halfSize);

      // 计算源区域的宽高，确保不会超出图片边界
      const sourceWidth = Math.min(size, imageInfo.width - sourceX);
      const sourceHeight = Math.min(size, imageInfo.height - sourceY);

      // 计算目标区域的左上角坐标
      const destX = x - sourceX;
      const destY = y - sourceY;

      this.log('边缘取色区域:', {
        sourceX, sourceY,
        sourceWidth, sourceHeight,
        destX, destY,
        x, y
      });

      // 创建图片对象用于Canvas 2D API
      const img = this.offscreenCanvas.createImage();
      img.onload = () => {
        // 绘制图片的边缘区域
        ctx.drawImage(
          img,
          sourceX,
          sourceY,
          sourceWidth,
          sourceHeight,
          0,
          0,
          sourceWidth,
          sourceHeight
        );

        // 获取指定像素的颜色 - 使用相对于Canvas的坐标
        try {
          const pixelX = Math.min(destX, sourceWidth - 1);
          const pixelY = Math.min(destY, sourceHeight - 1);
          const imageData = ctx.getImageData(pixelX, pixelY, 1, 1);
          const data = imageData.data;

          // 提取RGB值
          const r = data[0];
          const g = data[1];
          const b = data[2];

          // 转换为HEX格式
          const hexColor = '#' + this.rgbToHex(r, g, b);
          const rgbColor = `rgb(${r},${g},${b})`;

          // 更新颜色值
          this.setData({
            pickedColor: hexColor,
            pickedRgb: rgbColor
          });

          this.log('边缘取色成功:', { hexColor, rgbColor });
        } catch (err) {
          console.error('边缘取色失败:', err);
          // 如果边缘取色失败，使用固定的边缘颜色
          this.useFixedEdgeColor(x, y);
        }
      };
      img.onerror = () => {
        console.error('边缘取色图片加载失败');
        this.useFixedEdgeColor(x, y);
      };
      img.src = imagePath;
    } catch (error) {
      // 边缘取色失败，静默处理
      this.log('边缘取色错误详情:', error);

      // 如果边缘取色失败，使用固定的边缘颜色
      this.useFixedEdgeColor(x, y);

      // 不显示错误提示，因为这是备用方法，已经有主方法的错误提示了
    }
  },

  /**
   * 使用固定的边缘颜色 - 最后的备用方法
   */
  useFixedEdgeColor: function(x, y) {
    const { imageInfo } = this.data;
    if (!imageInfo) return;

    // 根据坐标位置，确定使用哪个边缘的颜色
    let edgeColor;

    // 左边缘
    if (x <= 0) {
      edgeColor = '#000000'; // 黑色
    }
    // 右边缘
    else if (x >= imageInfo.width - 1) {
      edgeColor = '#FFFFFF'; // 白色
    }
    // 上边缘
    else if (y <= 0) {
      edgeColor = '#808080'; // 灰色
    }
    // 下边缘
    else if (y >= imageInfo.height - 1) {
      edgeColor = '#C0C0C0'; // 银色
    }
    // 默认
    else {
      edgeColor = '#FF0000'; // 红色
    }

    // 更新颜色值
    this.setData({
      pickedColor: edgeColor,
      pickedRgb: this.hexToRgb(edgeColor)
    });

    this.log('使用固定边缘颜色:', edgeColor);
  },

  /**
   * RGB转HEX
   */
  rgbToHex: function(r, g, b) {
    return ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();
  },

  /**
   * HEX转RGB
   */
  hexToRgb: function(hex) {
    // 移除#号
    hex = hex.replace(/^#/, '');

    // 解析RGB值
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    return `rgb(${r},${g},${b})`;
  },

  /**
   * 复制颜色值
   */
  copyColorValue: function() {
    if (this.data.pickedColor) {
      wx.setClipboardData({
        data: this.data.pickedColor,
        success: () => {
          wx.showToast({
            title: '颜色值已复制',
            icon: 'success'
          });
        }
      });
    }
  },

  /**
   * 复制RGB值
   */
  copyRgbValue: function() {
    if (this.data.pickedRgb) {
      wx.setClipboardData({
        data: this.data.pickedRgb,
        success: () => {
          wx.showToast({
            title: 'RGB值已复制',
            icon: 'success'
          });
        }
      });
    }
  },

  /**
   * 确认颜色
   */
  confirmColor: function() {
    const { pickedColor, pickedRgb } = this.data;

    // 如果有事件通道，则通过事件通道返回颜色值
    if (this.eventChannel) {
      // 使用与colorPicker组件中监听的事件名称相匹配的名称
      // 添加confirmed标志，表示用户已确认颜色，不需要再次确认
      this.eventChannel.emit('acceptColorFromImagePicker', {
        color: pickedColor,
        rgb: pickedRgb,
        confirmed: true // 添加confirmed标志，表示用户已确认颜色
      });

      this.log('已通过事件通道发送颜色(已确认):', pickedColor);
    } else {
      // 没有找到事件通道，静默处理
    }

    // 返回上一页
    wx.navigateBack();
  }
});
