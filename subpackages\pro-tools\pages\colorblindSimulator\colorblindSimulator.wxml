<!--pages/colorblindSimulator/colorblindSimulator.wxml-->
<view class="page">

  <view class="container">
    <!-- 色盲类型说明 -->
    <view class="section info-section">
      <view class="section-title">色盲类型说明</view>
      <view class="info-content">
        <view class="info-intro">色盲是一种视觉障碍，影响人们区分某些颜色的能力。了解不同类型的色盲有助于设计更具包容性的界面。</view>
        <view class="info-grid">
          <view class="info-block">
            <view class="info-block-title">
              <view class="color-dot red-dot"></view>
              红色盲 (Protanopia)
            </view>
            <view class="info-block-text">无法感知红色光谱，红色看起来更暗，与绿色难以区分。</view>
          </view>
          <view class="info-block">
            <view class="info-block-title">
              <view class="color-dot green-dot"></view>
              绿色盲 (Deuteranopia)
            </view>
            <view class="info-block-text">无法感知绿色光谱，绿色与红色、棕色难以区分。</view>
          </view>
          <view class="info-block">
            <view class="info-block-title">
              <view class="color-dot blue-dot"></view>
              蓝色盲 (Tritanopia)
            </view>
            <view class="info-block-text">无法感知蓝色光谱，蓝色与绿色难以区分，紫色看起来像红色。</view>
          </view>
          <view class="info-block">
            <view class="info-block-title">
              <view class="color-dot gray-dot"></view>
              全色盲 (Achromatopsia)
            </view>
            <view class="info-block-text">完全无法感知颜色，只能看到黑白灰。</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 颜色输入部分 -->
    <view class="section input-section">
      <view class="section-title">输入颜色</view>
      <view class="base-color-container">
        <!-- 左侧颜色色块，在中央显示16进制色值 -->
        <view class="color-preview" style="background-color: {{inputColor}};" bindtap="showColorPicker">
          <view class="color-hex-value" style="color: {{textColor}};">{{inputColor}}</view>
        </view>
        <!-- 右侧上下并列的按钮 -->
        <view class="color-actions-column">
          <view class="btn-wrapper">
            <view class="custom-btn random-btn" bindtap="randomColor">随机颜色</view>
          </view>
          <view class="btn-wrapper">
            <view class="custom-btn picker-btn" bindtap="showColorPicker">选择颜色</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 模拟结果 -->
    <view class="section result-section">
      <view class="section-title">模拟结果</view>
      <view class="result-intro">点击任意色块可复制对应的颜色值</view>
      <view class="result-grid">
        <view
          wx:for="{{colorblindTypes}}"
          wx:key="id"
          class="result-item {{item.id === 'normal' ? 'result-item-normal' : ''}}"
          bindtap="copyColor"
          data-type="{{item.id}}"
        >
          <view class="result-color-container">
            <view class="result-color" style="background-color: {{simulatedColors[item.id]}};"></view>
          </view>
          <view class="result-info">
            <view class="result-name">
              <text class="result-name-text">{{item.name}}</text>
              <text wx:if="{{item.id === 'normal'}}" class="result-name-badge">原色</text>
            </view>
            <view class="result-value">{{simulatedColors[item.id]}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 颜色选择器弹窗 -->
  <view class="color-picker-modal" wx:if="{{showColorPicker}}">
    <view class="color-picker-container">
      <view class="color-picker-header">
        <view class="color-picker-title">选择颜色</view>
        <view class="color-picker-close" bindtap="hideColorPicker">×</view>
      </view>
      <!-- 集成颜色选择器组件 -->
      <color-picker
        color="{{inputColor}}"
        bindchange="onColorPickerChange"
        bindconfirm="onColorPickerConfirm"
        bindcancel="hideColorPicker"
      ></color-picker>
    </view>
  </view>
</view>
