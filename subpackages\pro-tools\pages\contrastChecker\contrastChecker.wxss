/* pages/contrastChecker/contrastChecker.wxss */
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
}

.container {
  flex: 1;
  padding: 30rpx 24rpx;
  overflow-y: auto;
  box-sizing: border-box;
}

/* 通用部分样式 */
.section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
  width: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.section:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.04);
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #191919;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background-color: #07c160;
  border-radius: 3rpx;
}

/* 颜色选择部分 */
.color-section {
  margin-bottom: 24rpx;
}

.color-cards {
  display: flex;
  flex-direction: column;
}

.color-cards-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4rpx;
}

.color-card {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  position: relative;
  flex: 1;
  width: 0; /* 确保flex布局下宽度平均分配 */
  min-width: 0; /* 防止内容溢出 */
  max-width: calc(50% - 12rpx); /* 确保两个卡片平均分配空间 */
}

.color-card.active {
  border-color: #07c160;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.15);
}

.color-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.color-card-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #191919;
}

.color-card-copy {
  font-size: 22rpx;
  color: #666666;
  padding: 4rpx 10rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  position: relative;
  transition: all 0.3s ease;
}

.color-card-copy:active {
  background-color: #e8e8e8;
  transform: scale(0.95);
}

.copy-icon {
  font-size: 22rpx;
}

.color-card-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.color-preview-large {
  width: 100%;
  height: 100rpx;
  border-radius: 8rpx;
  border: 1rpx solid #eeeeee;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.color-preview-large:active {
  transform: scale(0.98);
}

.color-preview-icon {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  background-color: rgba(0, 0, 0, 0.2);
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.color-preview-large:active .color-preview-icon {
  opacity: 1;
}

.color-input-group {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.color-input {
  width: 100%;
  height: 60rpx;
  border: 1rpx solid #eeeeee;
  border-radius: 8rpx;
  padding: 0 12rpx;
  font-size: 24rpx;
  color: #333333;
  font-family: -apple-system-monospace, monospace;
  background-color: #f9f9f9;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.color-actions {
  display: flex;
  gap: 8rpx;
}

.action-btn {
  flex: 1;
  font-size: 24rpx;
  padding: 8rpx 0;
  background-color: #f5f5f5;
  color: #333333;
  border-radius: 8rpx;
  border: none;
  line-height: 1.5;
  min-height: 0; /* 覆盖微信小程序按钮的默认最小高度 */
  transition: all 0.3s ease;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.action-btn.primary {
  background-color: #07c160;
  color: #ffffff;
  box-shadow: 0 2rpx 6rpx rgba(7, 193, 96, 0.2);
}

.action-btn:active {
  opacity: 0.8;
  transform: translateY(1rpx);
}

.swap-btn-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
  z-index: 2;
  flex-shrink: 0;
  padding: 0 8rpx;
  width: 48rpx;
}

.swap-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #07c160;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  padding: 0;
  line-height: 1;
  min-height: 0; /* 覆盖微信小程序按钮的默认最小高度 */
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.3);
}

.swap-btn:active {
  transform: rotate(180deg) scale(0.9);
  background: #09e374;
}

.swap-icon {
  font-size: 32rpx;
  font-weight: bold;
}

/* 历史记录样式 */
.color-history {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #eeeeee;
  width: 100%;
}

.history-title {
  font-size: 24rpx;
  color: #888888;
  margin-bottom: 12rpx;
}

.history-scroll {
  width: 100%;
  white-space: nowrap;
  overflow-x: auto;
}

.history-list {
  display: inline-flex;
  padding: 8rpx 0;
}

.history-item {
  display: inline-block;
  width: 56rpx;
  height: 56rpx;
  margin-right: 12rpx;
  border-radius: 4rpx;
  border: 1rpx solid #eeeeee;
  transition: transform 0.2s ease;
  flex-shrink: 0;
}

.history-item:active {
  transform: scale(0.92);
}

/* 对比度结果部分 */
.result-section {
  margin-bottom: 24rpx;
}

.contrast-result {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.contrast-ratio-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  margin-bottom: 40rpx;
  width: 100%;
}

.contrast-level-indicator {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: 16rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.contrast-level-indicator::before {
  content: "";
  position: absolute;
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background-color: #ffffff;
}

.contrast-level-indicator.excellent {
  background: linear-gradient(135deg, #07c160, #09e374);
}

.contrast-level-indicator.good {
  background: linear-gradient(135deg, #32CD32, #7CFC00);
}

.contrast-level-indicator.fair {
  background: linear-gradient(135deg, #FFA500, #FFD700);
}

.contrast-level-indicator.poor {
  background: linear-gradient(135deg, #FF4500, #FF6347);
}

.contrast-ratio-value {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 8rpx;
}

.contrast-number {
  font-size: 80rpx;
  font-weight: 700;
  color: #191919;
  line-height: 1;
}

.contrast-symbol {
  font-size: 40rpx;
  font-weight: 500;
  color: #666666;
  margin-left: 8rpx;
}

.contrast-level-text {
  font-size: 28rpx;
  font-weight: 600;
  padding: 6rpx 24rpx;
  border-radius: 20rpx;
  margin-top: 8rpx;
}

.contrast-level-text.excellent {
  background-color: rgba(7, 193, 96, 0.1);
  color: #07c160;
}

.contrast-level-text.good {
  background-color: rgba(50, 205, 50, 0.1);
  color: #32CD32;
}

.contrast-level-text.fair {
  background-color: rgba(255, 165, 0, 0.1);
  color: #FFA500;
}

.contrast-level-text.poor {
  background-color: rgba(255, 69, 0, 0.1);
  color: #FF4500;
}

.copy-btn {
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  background-color: #07c160;
  color: #ffffff;
  border-radius: 30rpx;
  border: none;
  margin-top: 16rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
  position: relative;
  overflow: hidden;
  font-weight: 500;
  letter-spacing: 1rpx;
}

.copy-btn:active {
  opacity: 0.8;
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(7, 193, 96, 0.2);
}

/* 复制提示 */
.copy-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 10;
}

.copy-indicator.show {
  opacity: 1;
}

.copy-indicator-text {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 500;
  background-color: rgba(7, 193, 96, 0.9);
  padding: 8rpx 16rpx;
  border-radius: 4rpx;
}

/* 文本预览部分 */
.preview-section {
  margin-bottom: 24rpx;
}

.font-controls {
  margin-bottom: 24rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.control-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
  text-align: center;
}

.font-weight-controls {
  margin-bottom: 0;
}

.font-weight-list {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.font-weight-item {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  color: #666666;
  background-color: #ffffff;
  border-radius: 24rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  flex: 1;
  text-align: center;
  max-width: 160rpx;
}

.font-weight-item.active {
  background-color: #07c160;
  color: #ffffff;
  font-weight: 500;
  box-shadow: 0 3rpx 8rpx rgba(7, 193, 96, 0.3);
}

.text-preview {
  padding: 24rpx;
  border-radius: 12rpx;
  min-height: 200rpx;
  line-height: 1.6;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #eeeeee;
  display: flex;
  flex-direction: column;
}

.text-preview .title-text {
  font-size: 16px;
  font-weight: bold;
  display: block;
}

.text-preview .normal-text {
  font-size: 12px;
  font-weight: normal;
  display: block;
}

.text-preview .bold-text {
  font-size: 14px;
  font-weight: bold;
  display: inline;
}

/* 颜色选择器弹窗 */
.color-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.color-picker-container {
  width: 92%;
  max-width: 650rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
  transform: scale(1);
  transition: transform 0.3s ease;
  animation: scaleIn 0.3s ease;
}

@keyframes scaleIn {
  from { transform: scale(0.9); }
  to { transform: scale(1); }
}

.color-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 28rpx;
  border-bottom: 1rpx solid #eeeeee;
  background-color: #f9f9f9;
}

.color-picker-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #191919;
}

.color-picker-close {
  font-size: 36rpx;
  color: #666666;
  line-height: 1;
  padding: 10rpx;
  margin: -10rpx;
  transition: all 0.3s ease;
}

.color-picker-close:active {
  transform: scale(0.9);
  color: #333333;
}
