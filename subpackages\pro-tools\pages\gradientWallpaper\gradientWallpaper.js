// pages/gradientWallpaper/gradientWallpaper.js

// 导入渐变色数据
const gradientData = require('../../../../data/gradientData.js');
const GRADIENT_DATA = gradientData.processedData;
const loadingUtils = require('../../../../utils/loadingUtils');

Page({
  data: {
    currentIndex: 0,
    currentGradient: null,
    gradientData: GRADIENT_DATA,
    showDownloadTip: false,
    statusBarHeight: getApp().globalData.statusBarHeight || 20,
    navBarHeight: getApp().globalData.navBarHeight || 44,
    // 是否显示CSS样式文本
    showCssText: false,
    // 壁纸数据
    wallpapers: [],
    // 是否显示保存成功提示
    showSaveSuccess: false
  },

  onLoad: function (options) {
    try {
      // 初始化标志变量
      this.isGenerating = false; // 是否正在生成新壁纸
      this.isSaving = false; // 是否正在保存壁纸

      // 如果有指定的渐变ID，则显示该渐变
      if (options && options.id) {
        const index = GRADIENT_DATA.findIndex(item => item.id === options.id);
        if (index !== -1) {
          const gradient = GRADIENT_DATA[index];
          // 初始化壁纸数据，确保包含指定的渐变
          this.initWallpapers(gradient);
          return;
        }
      }

      // 否则初始化随机壁纸数据
      this.initWallpapers();
    } catch (error) {
      // 显示错误提示
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  // 页面显示时触发
  onShow: function() {
    // 尝试禁用一些可能导致问题的功能
    try {
      if (wx.reportPerformance) {
        wx.reportPerformance(1001, 0);
      }
    } catch (error) {
      // 性能上报失败，忽略错误
    }
  },

  // 初始化壁纸数据
  initWallpapers: function(specifiedGradient) {
    // 从渐变数据中随机选择5个
    const wallpapers = [];
    const gradients = [...GRADIENT_DATA];

    // 如果指定了特定渐变，确保它在壁纸列表中
    if (specifiedGradient) {
      // 将指定的渐变放在第一位
      wallpapers.push(specifiedGradient);

      // 随机打乱剩余数组
      for (let i = gradients.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [gradients[i], gradients[j]] = [gradients[j], gradients[i]];
      }

      // 添加其他不重复的渐变，直到达到5个
      for (let i = 0; i < gradients.length && wallpapers.length < 5; i++) {
        // 确保不添加重复的渐变
        if (gradients[i].id !== specifiedGradient.id) {
          wallpapers.push(gradients[i]);
        }
      }
    } else {
      // 随机打乱数组
      for (let i = gradients.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [gradients[i], gradients[j]] = [gradients[j], gradients[i]];
      }

      // 取前5个作为初始壁纸
      for (let i = 0; i < 5 && i < gradients.length; i++) {
        wallpapers.push(gradients[i]);
      }
    }

    // 确保设置当前索引和当前渐变
    const currentIndex = 0;
    const currentGradient = wallpapers[0];



    // 批量更新数据，确保数据一致性
    this.setData({
      wallpapers: wallpapers,
      currentIndex: currentIndex,
      currentGradient: currentGradient
    }, () => {
      // 在setData回调中验证数据一致性，避免使用setTimeout
      if (this.data.currentGradient.id !== this.data.wallpapers[this.data.currentIndex].id) {
        this.setData({
          currentGradient: this.data.wallpapers[this.data.currentIndex]
        });
      }
    });
  },

  // 切换到下一个渐变
  nextGradient: function () {
    let nextIndex = this.data.currentIndex + 1;
    if (nextIndex >= this.data.wallpapers.length) {
      nextIndex = 0;
    }
    this.setData({
      currentIndex: nextIndex,
      currentGradient: this.data.wallpapers[nextIndex],
      showCssText: false
    });
  },

  // 切换到上一个渐变
  prevGradient: function () {
    let prevIndex = this.data.currentIndex - 1;
    if (prevIndex < 0) {
      prevIndex = this.data.wallpapers.length - 1;
    }
    this.setData({
      currentIndex: prevIndex,
      currentGradient: this.data.wallpapers[prevIndex],
      showCssText: false
    });
  },

  // 处理左滑事件
  handleSwipeLeft: function () {
    this.nextGradient();
  },

  // 处理右滑事件
  handleSwipeRight: function () {
    this.prevGradient();
  },

  // 保存渐变壁纸到相册
  saveGradient: async function () {
    try {
      // 检查是否正在生成新壁纸
      if (this.isGenerating) {
        wx.showToast({
          title: '请等待生成完成',
          icon: 'none',
          duration: 1500
        });
        return;
      }

      // 设置标志，表示正在保存
      this.isSaving = true;

      // 使用 loadingUtils 安全地管理加载状态
      await loadingUtils.withLoading(async () => {
        // 强制等待一小段时间，确保UI已经完全渲染
        await new Promise(resolve => setTimeout(resolve, 200));

        // 始终使用当前显示的壁纸 - 从currentGradient获取
        let gradient = this.data.currentGradient;

        // 确保我们有正确的当前壁纸
        const currentIndex = this.data.currentIndex;

        // 从wallpapers数组获取当前显示的壁纸（双重保险）
        if (this.data.wallpapers && this.data.wallpapers.length > 0) {
          if (currentIndex >= 0 && currentIndex < this.data.wallpapers.length) {
            const swiperGradient = this.data.wallpapers[currentIndex];

            // 确保currentGradient和wallpapers[currentIndex]一致
            if (!gradient || gradient.id !== swiperGradient.id) {
              gradient = swiperGradient;

              // 同步更新currentGradient
              this.setData({
                currentGradient: gradient
              });
            }
          } else {
            // 如果currentIndex无效，使用第一个壁纸
            gradient = this.data.wallpapers[0];

            // 同步更新currentGradient和currentIndex
            this.setData({
              currentGradient: gradient,
              currentIndex: 0
            });
          }
        }

        // 如果仍然没有获取到渐变数据，随机选择一个
        if (!gradient) {
          const randomIndex = Math.floor(Math.random() * GRADIENT_DATA.length);
          gradient = GRADIENT_DATA[randomIndex];

          // 更新当前数据
          this.setData({
            currentIndex: 0,
            currentGradient: gradient,
            wallpapers: [gradient]
          });
        }

        // 继续执行保存操作
        this.continueWithSave(gradient);
      }, {
        title: '生成中...',
        mask: true
      });
    } catch (error) {
      // 重置保存标志
      this.isSaving = false;

      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }
  },

  // 继续执行保存操作
  continueWithSave: function(gradient) {
    try {
      // 确保gradient有cssStyle属性
      if (!gradient || !gradient.cssStyle) {
        wx.showToast({
          title: '数据错误，请重试',
          icon: 'none'
        });
        console.error('保存壁纸 - 数据错误，没有cssStyle属性');
        // 重置保存标志
        this.isSaving = false;
        return;
      }

      // 记录要保存的壁纸信息
      console.log('继续保存 - 壁纸:', gradient.chineseName, 'ID:', gradient.id);

      // 检查是否有保存到相册的权限
      wx.getSetting({
        success: (res) => {
          if (!res.authSetting['scope.writePhotosAlbum']) {
            wx.authorize({
              scope: 'scope.writePhotosAlbum',
              success: () => {
                // 用户已经同意保存到相册，继续执行
                this.generateAndSaveImage(gradient);
              },
              fail: (err) => {
                // 用户拒绝授权
                wx.showModal({
                  title: '提示',
                  content: '需要您授权保存图片到相册',
                  showCancel: false,
                  success: function (res) {
                    if (res.confirm) {
                      wx.openSetting();
                    }
                  }
                });
              }
            });
          } else {
            // 用户已经授权，继续执行
            this.generateAndSaveImage(gradient);
          }
        },
        fail: (err) => {
          wx.showToast({
            title: '获取授权失败',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      // 保存渐变壁纸失败
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }
  },

  // 生成并保存图片
  generateAndSaveImage: function(gradient) {
    try {
      // 再次确认我们使用的是当前显示的壁纸
      if (this.data.currentGradient && this.data.currentGradient.id !== gradient.id) {
        // 检测到不一致，使用当前显示的壁纸
        gradient = this.data.currentGradient;
      }

      // 最后一次检查，确保使用的是当前swiper显示的壁纸
      const currentIndex = this.data.currentIndex;
      if (this.data.wallpapers &&
          this.data.wallpapers.length > 0) {

        // 检查currentIndex是否有效
        if (currentIndex >= 0 && currentIndex < this.data.wallpapers.length) {
          const swiperGradient = this.data.wallpapers[currentIndex];
          if (gradient.id !== swiperGradient.id) {
            // 最终检查发现不一致，使用swiper当前显示的壁纸
            gradient = swiperGradient;

            // 同步更新currentGradient
            this.setData({
              currentGradient: gradient
            });
          }
        } else {
          // 如果currentIndex无效，使用第一个壁纸
          gradient = this.data.wallpapers[0];

          // 同步更新currentGradient和currentIndex
          this.setData({
            currentGradient: gradient,
            currentIndex: 0
          });
        }
      }

      // 最终确认使用的壁纸

      // 记录日志，帮助调试

      // 创建画布上下文
      wx.createSelectorQuery()
        .select('#gradient-canvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          if (!res || !res[0] || !res[0].node) {
            wx.showToast({
              title: '画布创建失败，请重试',
              icon: 'none'
            });
            console.error('生成图片 - 画布创建失败');
            return;
          }

          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');

          // 设置画布尺寸为手机屏幕尺寸的2倍，以获得更高清的图像
          let windowInfo;
          try {
            windowInfo = wx.getWindowInfo();
          } catch (error) {
            // 如果getWindowInfo失败，使用默认值
            console.error('getWindowInfo失败，使用默认值', error);
            windowInfo = {
              windowWidth: 375,
              windowHeight: 667
            };
          }

          const canvasWidth = windowInfo.windowWidth * 2;
          const canvasHeight = windowInfo.windowHeight * 2;
          canvas.width = canvasWidth;
          canvas.height = canvasHeight;

          // 清空画布
          ctx.clearRect(0, 0, canvasWidth, canvasHeight);

          // 绘制渐变背景
          this.drawGradientBackground(ctx, gradient.cssStyle, canvasWidth, canvasHeight);
          console.log('生成图片 - 渐变背景绘制完成');

          // 绘制底部白色区域和CSS文本
          this.drawCssTextArea(ctx, gradient, canvasWidth, canvasHeight);

          // 将画布内容保存为图片
          wx.canvasToTempFilePath({
            canvas: canvas,
            success: (res) => {
              // 保存图片到相册
              wx.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  // 重置保存标志
                  this.isSaving = false;

                  // 只显示自定义的保存成功提示
                  this.setData({
                    showSaveSuccess: true
                  });

                  // 记录保存成功的壁纸信息
                  console.log('保存成功 - 壁纸:', gradient.chineseName, 'ID:', gradient.id);

                  // 2秒后隐藏提示
                  setTimeout(() => {
                    this.setData({
                      showSaveSuccess: false
                    });
                  }, 2000);
                },
                fail: (err) => {
                  // 重置保存标志
                  this.isSaving = false;

                  console.error('保存到相册失败', err);
                  wx.showToast({
                    title: '保存失败',
                    icon: 'none'
                  });
                }
              });
            },
            fail: (err) => {
              // 重置保存标志
              this.isSaving = false;

              console.error('生成临时文件失败', err);
              wx.showToast({
                title: '生成失败',
                icon: 'none'
              });
            }
          });
        });
    } catch (error) {
      // 重置保存标志
      this.isSaving = false;

      console.error('生成并保存图片失败', error);
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }
  },

  // 绘制渐变背景
  drawGradientBackground: function (ctx, cssStyle, width, height) {
    try {
      // 记录日志，帮助调试
      console.log('绘制渐变背景 - CSS样式:', cssStyle);

      // 解析CSS渐变样式
      let gradient;

      // 确保cssStyle是字符串
      if (!cssStyle || typeof cssStyle !== 'string') {
        console.error('绘制渐变背景 - CSS样式无效:', cssStyle);
        // 使用默认渐变
        gradient = ctx.createLinearGradient(0, 0, 0, height);
        gradient.addColorStop(0, '#ffffff');
        gradient.addColorStop(1, '#000000');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);
        return;
      }

      if (cssStyle.includes('linear-gradient')) {
        // 线性渐变
        let directionMatch = cssStyle.match(/linear-gradient\((.*?),/);
        if (!directionMatch || !directionMatch[1]) {
          console.error('绘制渐变背景 - 无法解析方向:', cssStyle);
          // 默认从上到下
          gradient = ctx.createLinearGradient(width / 2, 0, width / 2, height);
        } else {
          const direction = directionMatch[1];
          let x0, y0, x1, y1;

          // 根据方向设置渐变起点和终点
          if (direction.includes('to top')) {
            x0 = width / 2;
            y0 = height;
            x1 = width / 2;
            y1 = 0;
          } else if (direction.includes('to right')) {
            x0 = 0;
            y0 = height / 2;
            x1 = width;
            y1 = height / 2;
          } else if (direction.includes('-225deg') || direction.includes('-60deg')) {
            x0 = width;
            y0 = height;
            x1 = 0;
            y1 = 0;
          } else if (direction.includes('120deg') || direction.includes('45deg') || direction.includes('60deg')) {
            x0 = 0;
            y0 = height;
            x1 = width;
            y1 = 0;
          } else if (direction.includes('-20deg') || direction.includes('15deg')) {
            x0 = width / 2;
            y0 = height;
            x1 = width / 2;
            y1 = 0;
          } else if (direction.includes('135deg')) {
            x0 = 0;
            y0 = 0;
            x1 = width;
            y1 = height;
          } else {
            // 默认从上到下
            x0 = width / 2;
            y0 = 0;
            x1 = width / 2;
            y1 = height;
          }

          gradient = ctx.createLinearGradient(x0, y0, x1, y1);
        }
      } else if (cssStyle.includes('radial-gradient')) {
        // 径向渐变
        gradient = ctx.createRadialGradient(
          width / 2, height / 2, 0,
          width / 2, height / 2, width / 2
        );
      } else {
        // 默认使用线性渐变
        console.log('绘制渐变背景 - 使用默认线性渐变');
        gradient = ctx.createLinearGradient(0, 0, 0, height);
      }

    // 提取颜色和位置
    const colorStops = cssStyle.match(/(#[0-9A-Fa-f]{6}|#[0-9A-Fa-f]{3}|rgba?\([^)]+\)|[a-zA-Z]+)\s+(\d+%)/g);
    if (colorStops) {
      console.log('绘制渐变背景 - 找到颜色停止点:', colorStops.length);
      colorStops.forEach(stop => {
        const parts = stop.match(/(#[0-9A-Fa-f]{6}|#[0-9A-Fa-f]{3}|rgba?\([^)]+\)|[a-zA-Z]+)\s+(\d+)%/);
        if (parts) {
          const color = parts[1];
          const position = parseInt(parts[2]) / 100;
          gradient.addColorStop(position, color);
          console.log('绘制渐变背景 - 添加颜色停止点:', color, position);
        }
      });
    } else {
      // 如果无法解析颜色停止点，使用默认颜色
      console.log('绘制渐变背景 - 无法解析颜色停止点，使用默认颜色');
      gradient.addColorStop(0, '#ffffff');
      gradient.addColorStop(1, '#000000');
    }

    // 填充渐变
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
    console.log('绘制渐变背景 - 完成');
    } catch (error) {
      console.error('绘制渐变背景 - 发生错误:', error);
      // 使用默认渐变
      const defaultGradient = ctx.createLinearGradient(0, 0, 0, height);
      defaultGradient.addColorStop(0, '#ffffff');
      defaultGradient.addColorStop(1, '#000000');
      ctx.fillStyle = defaultGradient;
      ctx.fillRect(0, 0, width, height);
    }
  },

  // 绘制底部CSS文本区域
  drawCssTextArea: function (ctx, gradient, width, height) {
    // 底部白色区域高度
    const textAreaHeight = height * 0.18; // 增加高度以容纳名称
    const textAreaY = height - textAreaHeight;

    // 绘制白色背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, textAreaY, width, textAreaHeight);

    // 绘制渐变色名称
    ctx.fillStyle = '#333333';
    ctx.font = 'bold 36px sans-serif'; // 加粗字体
    ctx.textAlign = 'center';

    // 绘制中文名称
    const chineseName = gradient.chineseName || '';
    ctx.fillText(chineseName, width / 2, textAreaY + 50);

    // 绘制英文名称
    ctx.font = '28px sans-serif'; // 较小的字体
    const englishName = gradient.englishName || gradient.name || '';
    ctx.fillText(englishName, width / 2, textAreaY + 100);

    // 设置CSS文本样式
    ctx.fillStyle = '#666666';
    ctx.font = '24px sans-serif';
    ctx.textAlign = 'left'; // 左对齐

    // 绘制CSS文本
    const cssText = gradient.cssStyle;
    const maxWidth = width - 80; // 左右各留40px的边距
    const leftMargin = 40; // 左边距

    // 简单的文本换行处理
    const words = cssText.split(' ');
    let line = '';
    let y = textAreaY + 150; // CSS文本起始Y坐标

    for (let i = 0; i < words.length; i++) {
      const testLine = line + words[i] + ' ';
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;

      if (testWidth > maxWidth && i > 0) {
        ctx.fillText(line, leftMargin, y);
        line = words[i] + ' ';
        y += 36; // 行间距
      } else {
        line = testLine;
      }
    }
    ctx.fillText(line, leftMargin, y);
  },

  // 返回上一页
  navigateBack: function () {
    wx.navigateBack();
  },

  // 切换显示CSS文本
  toggleCssText: function () {
    this.setData({
      showCssText: !this.data.showCssText
    });
  },

  // 随机生成新壁纸
  generateNewWallpaper: function() {
    // 添加防抖，避免连续快速点击
    if (this.isGenerating) {
      console.log('随机生成 - 正在生成中，忽略此次点击');
      return;
    }

    // 设置标志，表示正在生成
    this.isGenerating = true;

    // 不显示加载提示，直接生成新壁纸

    // 从渐变数据中随机选择5个
    const wallpapers = [];
    const gradients = [...GRADIENT_DATA];

    // 随机打乱数组
    for (let i = gradients.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [gradients[i], gradients[j]] = [gradients[j], gradients[i]];
    }

    // 取前5个作为新的壁纸
    for (let i = 0; i < 5 && i < gradients.length; i++) {
      wallpapers.push(gradients[i]);
    }

    // 记录新生成的第一个壁纸
    const newGradient = wallpapers[0];

    // 更新数据
    this.setData({
      wallpapers: wallpapers,
      currentIndex: 0,
      currentGradient: newGradient,
      showCssText: false
    });

    // 延迟一段时间后再次确认数据一致性，并隐藏加载提示
    setTimeout(() => {
      // 再次确认数据一致性
      if (this.data.currentGradient.id !== this.data.wallpapers[0].id) {
        // 数据不一致，重新同步
        this.setData({
          currentGradient: this.data.wallpapers[0],
          currentIndex: 0
        });
      }

      // 重置标志，表示生成完成
      this.isGenerating = false;
    }, 300);
  },

  // 处理滑动切换事件
  onSwiperChange: function(e) {
    const currentIndex = e.detail.current;

    // 确保索引有效
    if (currentIndex >= 0 && currentIndex < this.data.wallpapers.length) {
      const currentGradient = this.data.wallpapers[currentIndex];

      // 记录日志，帮助调试

      // 同步更新数据 - 确保currentGradient被正确更新
      this.setData({
        currentIndex: currentIndex,
        currentGradient: currentGradient,
        showCssText: false
      });

      // 额外检查确保数据一致性
      setTimeout(() => {
        if (this.data.currentGradient.id !== this.data.wallpapers[this.data.currentIndex].id) {
          // 数据不一致，重新同步
          this.setData({
            currentGradient: this.data.wallpapers[this.data.currentIndex]
          });
        }
      }, 50);
    } else {
      // 滑动切换 - 索引无效，静默处理
    }
  },

  // 保存壁纸到相册
  saveWallpaper: function() {
    this.saveGradient();
  }
});
