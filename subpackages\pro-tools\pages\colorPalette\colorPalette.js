// pages/colorPalette/colorPalette.js - 配色方案
const colorUtils = require('../../utils/colorUtils');
Page({
  data: {
    baseColor: '#c83c23', // 默认基础颜色（中国红）
    paletteType: 'complementary', // 默认配色方案类型
    paletteTypes: [
      { id: 'complementary', name: '互补色' },
      { id: 'analogous', name: '类似色' },
      { id: 'triadic', name: '三角色' },
      { id: 'splitComplementary', name: '分裂互补色' },
      { id: 'tetradic', name: '四角色' },
      { id: 'monochromatic', name: '单色调' }
    ],
    colorPalette: [], // 生成的配色方案
    coreColors: [], // 核心颜色，用于在色轮下方显示
    showColorPicker: false, // 是否显示颜色选择器
    copiedIndex: -1 // 当前复制的颜色索引，用于显示复制提示
  },

  onLoad: function (options) {
    // 获取默认配色方案名称
    const defaultPaletteType = this.data.paletteType;
    const paletteType = this.data.paletteTypes.find(item => item.id === defaultPaletteType);
    const typeName = paletteType ? paletteType.name : '';

    // 计算基础颜色对应的文字颜色
    const textColor = colorUtils.getTextColorForBackground(this.data.baseColor);

    this.setData({
      currentPaletteTypeName: typeName,
      textColor: textColor
    });

    // 页面加载时生成默认配色方案
    this.generatePalette();
  },

  onReady: function() {
    // 页面渲染完成后，获取色轮组件实例
    this.colorWheel = this.selectComponent('#colorWheel');
    console.log('色轮组件实例获取状态:', this.colorWheel ? '成功' : '失败');
  },

  onShow: function() {
    // 页面显示时，检查色轮组件是否已初始化
    if (this.colorWheel) {
      console.log('页面显示，检查色轮组件');
      // 可以在这里添加额外的初始化逻辑
    }

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '配色方案生成器'
    });
  },

  // 用户点击右上角分享或使用分享按钮
  onShareAppMessage: function() {
    const paletteType = this.data.paletteTypes.find(item => item.id === this.data.paletteType);
    const typeName = paletteType ? paletteType.name : '配色方案';

    return {
      title: typeName + ' - 专业配色方案生成器',
      path: '/pages/colorPalette/colorPalette?type=' + this.data.paletteType + '&color=' + encodeURIComponent(this.data.baseColor),
      imageUrl: '/assets/images/share-color-palette.png' // 分享图片
    };
  },

  // 切换配色方案类型
  switchPaletteType: function(e) {
    const type = e.currentTarget.dataset.type;

    // 获取配色方案名称
    const paletteType = this.data.paletteTypes.find(item => item.id === type);
    const typeName = paletteType ? paletteType.name : '';

    this.setData({
      paletteType: type,
      currentPaletteTypeName: typeName
    });
    this.generatePalette();
  },

  // 显示颜色选择器
  showColorPicker: function() {
    this.setData({
      showColorPicker: true
    });
  },

  // 隐藏颜色选择器
  hideColorPicker: function() {
    this.setData({
      showColorPicker: false
    });
  },

  // 颜色选择器变化
  onColorPickerChange: function(e) {
    const color = e.detail.color;
    const textColor = colorUtils.getTextColorForBackground(color);
    // 实时更新颜色预览，但不关闭选择器
    this.setData({
      baseColor: color,
      textColor: textColor
    });
    this.generatePalette();

    // 更新色轮组件
    if (this.colorWheel) {
      this.colorWheel.updateSelectedPoint(color);
    }
  },

  // 颜色选择器确认
  onColorPickerConfirm: function(e) {
    const color = e.detail.color;
    const textColor = colorUtils.getTextColorForBackground(color);
    this.setData({
      baseColor: color,
      textColor: textColor,
      showColorPicker: false
    });
    this.generatePalette();

    // 更新色轮组件
    if (this.colorWheel) {
      this.colorWheel.updateSelectedPoint(color);
    }

    // 显示轻提示
    wx.showToast({
      title: '颜色已更新',
      icon: 'success',
      duration: 1000
    });
  },

  // 色轮颜色变化
  onColorWheelChange: function(e) {
    const color = e.detail.color;
    const textColor = colorUtils.getTextColorForBackground(color);
    this.setData({
      baseColor: color,
      textColor: textColor
    });
    this.generatePalette();
  },

  // 生成配色方案
  generatePalette: function() {
    const { baseColor, paletteType } = this.data;
    let palette = [];
    let coreColors = []; // 核心颜色，用于在色轮下方显示

    // 将HEX转换为HSL
    const hsl = colorUtils.hexToHSL(baseColor);

    switch(paletteType) {
      case 'complementary':
        // 互补色：色相相差180度
        palette = [
          colorUtils.hslToHex(hsl.h, hsl.s, hsl.l), // 基础色
          colorUtils.hslToHex((hsl.h + 180) % 360, hsl.s, hsl.l) // 互补色
        ];
        // 设置核心颜色 - 互补色方案显示基础色和互补色
        coreColors = [
          colorUtils.hslToHex(hsl.h, hsl.s, hsl.l), // 基础色
          colorUtils.hslToHex((hsl.h + 180) % 360, hsl.s, hsl.l) // 互补色
        ];
        // 添加额外的变体
        palette.push(colorUtils.hslToHex(hsl.h, Math.max(hsl.s - 20, 0), Math.min(hsl.l + 15, 100))); // 基础色变体
        palette.push(colorUtils.hslToHex((hsl.h + 180) % 360, Math.max(hsl.s - 20, 0), Math.min(hsl.l + 15, 100))); // 互补色变体
        palette.push(colorUtils.hslToHex(hsl.h, Math.min(hsl.s + 10, 100), Math.max(hsl.l - 15, 0))); // 基础色深色变体
        break;
      case 'analogous':
        // 类似色：色相相差30度
        palette = [
          colorUtils.hslToHex((hsl.h - 30 + 360) % 360, hsl.s, hsl.l), // 左侧类似色
          colorUtils.hslToHex(hsl.h, hsl.s, hsl.l), // 基础色
          colorUtils.hslToHex((hsl.h + 30) % 360, hsl.s, hsl.l) // 右侧类似色
        ];
        // 设置核心颜色 - 类似色方案显示左侧类似色、基础色和右侧类似色
        coreColors = [
          colorUtils.hslToHex((hsl.h - 30 + 360) % 360, hsl.s, hsl.l), // 左侧类似色
          colorUtils.hslToHex(hsl.h, hsl.s, hsl.l), // 基础色
          colorUtils.hslToHex((hsl.h + 30) % 360, hsl.s, hsl.l) // 右侧类似色
        ];
        // 添加额外的变体
        palette.push(colorUtils.hslToHex((hsl.h - 15 + 360) % 360, Math.max(hsl.s - 10, 0), Math.min(hsl.l + 15, 100))); // 左侧中间色
        palette.push(colorUtils.hslToHex((hsl.h + 15) % 360, Math.max(hsl.s - 10, 0), Math.min(hsl.l + 15, 100))); // 右侧中间色
        break;
      case 'triadic':
        // 三角色：色相相差120度
        palette = [
          colorUtils.hslToHex(hsl.h, hsl.s, hsl.l), // 基础色
          colorUtils.hslToHex((hsl.h + 120) % 360, hsl.s, hsl.l), // 三角色1
          colorUtils.hslToHex((hsl.h + 240) % 360, hsl.s, hsl.l) // 三角色2
        ];
        // 设置核心颜色 - 三角色方案显示基础色和两个三角色
        coreColors = [
          colorUtils.hslToHex(hsl.h, hsl.s, hsl.l), // 基础色
          colorUtils.hslToHex((hsl.h + 120) % 360, hsl.s, hsl.l), // 三角色1
          colorUtils.hslToHex((hsl.h + 240) % 360, hsl.s, hsl.l) // 三角色2
        ];
        // 添加额外的变体
        palette.push(colorUtils.hslToHex(hsl.h, Math.max(hsl.s - 15, 0), Math.min(hsl.l + 20, 100))); // 基础色亮色变体
        palette.push(colorUtils.hslToHex((hsl.h + 120) % 360, Math.max(hsl.s - 15, 0), Math.min(hsl.l + 20, 100))); // 三角色1亮色变体
        break;
      case 'splitComplementary':
        // 分裂互补色：互补色的两侧各30度
        palette = [
          colorUtils.hslToHex(hsl.h, hsl.s, hsl.l), // 基础色
          colorUtils.hslToHex((hsl.h + 150) % 360, hsl.s, hsl.l), // 分裂互补色1
          colorUtils.hslToHex((hsl.h + 210) % 360, hsl.s, hsl.l) // 分裂互补色2
        ];
        // 设置核心颜色 - 分裂互补色方案显示基础色和两个分裂互补色
        coreColors = [
          colorUtils.hslToHex(hsl.h, hsl.s, hsl.l), // 基础色
          colorUtils.hslToHex((hsl.h + 150) % 360, hsl.s, hsl.l), // 分裂互补色1
          colorUtils.hslToHex((hsl.h + 210) % 360, hsl.s, hsl.l) // 分裂互补色2
        ];
        // 添加额外的变体
        palette.push(colorUtils.hslToHex(hsl.h, Math.max(hsl.s - 20, 0), Math.min(hsl.l + 15, 100))); // 基础色亮色变体
        palette.push(colorUtils.hslToHex((hsl.h + 180) % 360, Math.max(hsl.s - 20, 0), Math.min(hsl.l + 15, 100))); // 互补色变体
        break;
      case 'tetradic':
        // 四角色：两组互补色，相差60度
        palette = [
          colorUtils.hslToHex(hsl.h, hsl.s, hsl.l), // 基础色
          colorUtils.hslToHex((hsl.h + 60) % 360, hsl.s, hsl.l), // 四角色1
          colorUtils.hslToHex((hsl.h + 180) % 360, hsl.s, hsl.l), // 四角色2（基础色的互补色）
          colorUtils.hslToHex((hsl.h + 240) % 360, hsl.s, hsl.l) // 四角色3（四角色1的互补色）
        ];
        // 设置核心颜色 - 四角色方案显示所有四个角色
        coreColors = [
          colorUtils.hslToHex(hsl.h, hsl.s, hsl.l), // 基础色
          colorUtils.hslToHex((hsl.h + 60) % 360, hsl.s, hsl.l), // 四角色1
          colorUtils.hslToHex((hsl.h + 180) % 360, hsl.s, hsl.l), // 四角色2
          colorUtils.hslToHex((hsl.h + 240) % 360, hsl.s, hsl.l) // 四角色3
        ];
        // 添加额外的变体
        palette.push(colorUtils.hslToHex(hsl.h, Math.max(hsl.s - 15, 0), Math.min(hsl.l + 20, 100))); // 基础色亮色变体
        break;
      case 'monochromatic':
        // 单色调：相同色相，不同亮度和饱和度
        palette = [
          colorUtils.hslToHex(hsl.h, hsl.s, Math.max(hsl.l - 30, 0)), // 暗色
          colorUtils.hslToHex(hsl.h, hsl.s, hsl.l), // 基础色
          colorUtils.hslToHex(hsl.h, Math.max(hsl.s - 30, 0), hsl.l), // 低饱和度
          colorUtils.hslToHex(hsl.h, hsl.s, Math.min(hsl.l + 30, 100)), // 亮色
          colorUtils.hslToHex(hsl.h, Math.min(hsl.s + 15, 100), Math.max(hsl.l - 15, 0)) // 深色高饱和度
        ];
        // 设置核心颜色 - 单色调方案显示暗色、基础色、亮色
        coreColors = [
          colorUtils.hslToHex(hsl.h, hsl.s, Math.max(hsl.l - 30, 0)), // 暗色
          colorUtils.hslToHex(hsl.h, hsl.s, hsl.l), // 基础色
          colorUtils.hslToHex(hsl.h, hsl.s, Math.min(hsl.l + 30, 100)) // 亮色
        ];
        break;
    }

    this.setData({
      colorPalette: palette,
      coreColors: coreColors
    });
  },

  // 复制颜色值
  copyColor: function(e) {
    const color = e.currentTarget.dataset.color;
    const index = e.currentTarget.dataset.index;

    // 设置当前复制的颜色索引，用于显示复制提示
    this.setData({
      copiedIndex: index
    });

    wx.setClipboardData({
      data: color,
      success: () => {
        // 显示轻提示
        wx.showToast({
          title: '颜色已复制',
          icon: 'success',
          duration: 1500
        });

        // 1.5秒后隐藏复制提示
        setTimeout(() => {
          this.setData({
            copiedIndex: -1
          });
        }, 1500);
      }
    });
  },

  // 随机生成基础颜色
  randomColor: function() {
    // 生成更美观的随机颜色 - 使用HSL模型
    const h = Math.floor(Math.random() * 360); // 随机色相
    const s = Math.floor(Math.random() * 40) + 60; // 60-100的饱和度
    const l = Math.floor(Math.random() * 30) + 35; // 35-65的亮度

    // 转换为HEX
    const color = colorUtils.hslToHex(h, s, l);
    const textColor = colorUtils.getTextColorForBackground(color);

    this.setData({
      baseColor: color,
      textColor: textColor
    });
    this.generatePalette();

    // 更新色轮组件
    if (this.colorWheel) {
      this.colorWheel.updateSelectedPoint(color);
    }
  },

  // 颜色转换函数已移至 utils/colorUtils.js
});
