// pages/ambientLight/ambientLight.js
const logUtils = require('../../../../utils/logUtils');
const loadingUtils = require('../../../../utils/loadingUtils');

// 节流函数 - 优化setData调用频率
function throttle(func, delay) {
  let timeoutId;
  let lastExecTime = 0;
  return function (...args) {
    const currentTime = Date.now();

    if (currentTime - lastExecTime > delay) {
      func.apply(this, args);
      lastExecTime = currentTime;
    } else {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func.apply(this, args);
        lastExecTime = Date.now();
      }, delay - (currentTime - lastExecTime));
    }
  };
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentColor: '#FFD1DC', // 默认柔光粉
    textColor: '#000000', // 文字颜色
    colorDescription: '柔光粉，提亮肤色增添气质', // 颜色描述
    isFullScreen: true, // 默认为全屏模式
    showControls: false, // 默认不显示控制面板
    showCamera: false, // 默认不显示摄像头
    devicePosition: 'back', // 默认使用后置摄像头
    showColorPicker: false, // 是否显示颜色选择器
    statusBarHeight: 0, // 状态栏高度
    hue: 0, // 色调 (0-360)
    saturation: 100, // 饱和度 (0-100)
    lightness: 50, // 明度 (0-100)
    screenBrightness: 80, // 屏幕亮度 (0-100)
    // 相机相关设置
    cameraFlash: 'off', // 闪光灯状态：auto, on, off
    cameraResolution: 'medium', // 分辨率：low, medium, high
    cameraFrameSize: 'medium', // 帧大小：small, medium, large
    showCameraSettings: false, // 是否显示相机设置
    cameraContext: null, // 相机上下文
    // 滑块触摸状态
    sliderTouching: false, // 是否正在触摸滑块
    currentSliderType: null, // 当前触摸的滑块类型
    presetColors: [
      { name: '柔光粉', color: '#FFD1DC', textColor: '#000000', description: '提亮肤色增添气质' },
      { name: '暖肤光', color: '#FFE4C4', textColor: '#000000', description: '温暖肤色增添光泽' },
      { name: '冷白光', color: '#E0FFFF', textColor: '#000000', description: '清爽通透不刺眼' },
      { name: '日落橙', color: '#FFA07A', textColor: '#000000', description: '温暖橙光营造氛围' },
      { name: '薄荷绿', color: '#98FB98', textColor: '#000000', description: '清新自然减轻疲劳' },
      { name: '梦幻紫', color: '#E6E6FA', textColor: '#000000', description: '柔和梦幻增添情调' },
      { name: '纯白光', color: '#FFFFFF', textColor: '#000000', description: '明亮清晰不偏色' },
      { name: '暖黄光', color: '#FFFACD', textColor: '#000000', description: '温馨舒适减轻疲劳' },
      { name: '奶茶棕', color: '#D2B48C', textColor: '#000000', description: '温暖柔和不刺眼' },
      { name: '天空蓝', color: '#87CEEB', textColor: '#000000', description: '清新明亮提升专注' },
      { name: '复古橙', color: '#FF7F50', textColor: '#000000', description: '温暖活力增添情调' },
      { name: '浪漫紫', color: '#9370DB', textColor: '#FFFFFF', description: '浪漫梦幻营造氛围' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    try {
      // 初始化节流函数 - 优化setData性能
      this.throttledSetData = throttle((data) => {
        this.setData(data);
      }, 16); // 约60fps的更新频率

      this.throttledAdjustColor = throttle(() => {
        this.adjustPresetColor();
      }, 50); // 颜色调整节流

      // 保持屏幕常亮
      wx.setKeepScreenOn({
        keepScreenOn: true,
        fail: (err) => {
          // 失败不阻止后续操作
        }
      });

      // 初始化色调值
      try {
        const hsl = this.hexToHSL(this.data.currentColor);
        this.setData({
          hue: hsl.h
        });
      } catch (error) {
        // 使用默认值
        this.setData({
          hue: 0
        });
      }

      // 获取状态栏高度 - 使用try-catch包装
      this.getStatusBarHeight();

      // 获取当前屏幕亮度 - 使用try-catch包装
      this.getCurrentScreenBrightness();

      // 检查相机权限
      this.checkCameraPermission();
    } catch (error) {
      // 确保页面至少能显示基本内容
      wx.showToast({
        title: '加载部分功能失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 检查相机权限
   */
  checkCameraPermission() {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.camera'] === false) {
          // 用户曾经拒绝过相机权限，提前提示用户
          wx.showModal({
            title: '提示',
            content: '使用补光灯拍照功能需要相机权限，请在设置中允许使用相机',
            confirmText: '我知道了',
            showCancel: false
          });
        } else if (res.authSetting['scope.camera'] === undefined) {
          // 用户还未被询问过相机权限，可以预先请求
          // 这里不主动请求权限，等用户点击相机按钮时再请求
        }
        // 用户已授权的情况不需要特殊处理
      },
      fail: (error) => {
        wx.showToast({
          title: '权限检查失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 获取状态栏高度
   */
  getStatusBarHeight() {
    try {
      // 优先使用全局系统信息
      const app = getApp();
      if (app && app.globalData && app.globalData.statusBarHeight) {
        this.setData({
          statusBarHeight: app.globalData.statusBarHeight
        });
        return;
      }

      // 如果全局信息不可用，使用新的API
      try {
        const windowInfo = wx.getWindowInfo();
        this.setData({
          statusBarHeight: windowInfo.statusBarHeight || 20
        });
      } catch (error) {
        console.warn('获取窗口信息失败，使用默认值:', error);
        // 使用默认值
        this.setData({
          statusBarHeight: 20
        });
      }
    } catch (error) {
      // 使用默认值
      this.setData({
        statusBarHeight: 20
      });
    }
  },

  /**
   * 获取当前屏幕亮度
   */
  getCurrentScreenBrightness() {
    try {
      wx.getScreenBrightness({
        success: (res) => {
          // 将0-1的值转换为0-100
          const brightness = Math.round(res.value * 100);
          this.setData({
            screenBrightness: brightness
          });
        },
        fail: (err) => {
          // 使用默认值
          this.setData({
            screenBrightness: 80
          });
        }
      });
    } catch (error) {
      // 使用默认值
      this.setData({
        screenBrightness: 80
      });
    }
  },

  /**
   * 调整屏幕亮度
   */
  onScreenBrightnessChange(e) {
    try {
      const value = e.detail.value;
      // 将0-100的值转换为0-1
      const brightness = value / 100;

      this.setData({
        screenBrightness: value
      });

      // 添加错误处理
      wx.setScreenBrightness({
        value: brightness,
        success: () => {
          // 成功设置亮度，不需要特殊处理
        },
        fail: (err) => {
          // 失败时不影响UI显示，用户仍然可以看到滑块位置
          wx.showToast({
            title: '设置亮度失败',
            icon: 'none',
            duration: 1000
          });
        }
      });
    } catch (error) {
      // 出现异常时，至少保证UI状态正确
      if (e && e.detail && typeof e.detail.value === 'number') {
        this.setData({
          screenBrightness: e.detail.value
        });
      }
      wx.showToast({
        title: '亮度调整异常',
        icon: 'none',
        duration: 1000
      });
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    try {
      // 重新设置屏幕常亮
      wx.setKeepScreenOn({
        keepScreenOn: true
      });

      // 如果相机已打开，尝试重新初始化相机上下文
      if (this.data.showCamera && !this.data.cameraContext) {
        try {
          const cameraContext = wx.createCameraContext('myCamera');
          this.setData({
            cameraContext: cameraContext
          });
        } catch (error) {
          // 不显示错误提示，避免干扰用户体验
        }
      }

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '补光灯 - 专业拍照补光'
      });
    } catch (error) {
      // 出错时不显示错误提示，避免干扰用户体验
    }
  },

  /**
   * 用户点击右上角分享或使用分享按钮
   */
  onShareAppMessage: function() {
    // 获取当前颜色名称
    const currentColorObj = this.data.presetColors.find(c => c.color === this.data.currentColor);
    const colorName = currentColorObj ? currentColorObj.name : '自定义色彩';

    return {
      title: colorName + ' - 补光灯',
      path: '/pages/ambientLight/ambientLight?color=' + encodeURIComponent(this.data.currentColor),
      imageUrl: '/assets/images/share-ambient-light.png' // 分享图片
    };
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    try {
      // 如果相机已打开，关闭相机
      if (this.data.showCamera) {
        this.setData({
          showCamera: false,
          cameraContext: null
        });
      }
    } catch (error) {
      // 出错时不显示错误提示，避免干扰用户体验
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    try {
      // 取消屏幕常亮
      wx.setKeepScreenOn({
        keepScreenOn: false
      });

      // 清除可能存在的计时器
      if (this.cameraInitTimeout) {
        clearTimeout(this.cameraInitTimeout);
        this.cameraInitTimeout = null;
      }

      // 清理节流函数，避免内存泄漏
      this.throttledSetData = null;
      this.throttledAdjustColor = null;
    } catch (error) {
      // 出错时不显示错误提示，避免干扰用户体验
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 导航返回函数（与goBack功能相同，但名称与其他页面保持一致）
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 切换控制面板显示状态
   */
  toggleControls() {
    this.setData({
      showControls: !this.data.showControls
    });
  },

  /**
   * 点击屏幕空白处
   */
  onScreenTap(e) {
    // 只在全屏模式下响应点击
    if (this.data.isFullScreen) {
      this.toggleControls();
    }
  },

  /**
   * 阻止事件冒泡的通用方法
   */
  preventTap(e) {
    // 阻止事件冒泡，用于防止点击子元素时触发父元素的事件
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
  },

  /**
   * 显示颜色选择器
   */
  showColorPicker() {
    this.setData({
      showColorPicker: true
    });
  },

  /**
   * 隐藏颜色选择器
   */
  hideColorPicker() {
    this.setData({
      showColorPicker: false
    });
  },

  /**
   * 相机初始化完成事件
   */
  onCameraInitDone(e) {
    try {
      // 清除可能存在的初始化超时计时器
      if (this.cameraInitTimeout) {
        clearTimeout(this.cameraInitTimeout);
        this.cameraInitTimeout = null;
      }

      // 延迟创建相机上下文，避免在某些机型上出现的时序问题
      setTimeout(() => {
        try {
          // 创建相机上下文并保存到data中
          const cameraContext = wx.createCameraContext('myCamera');
          this.setData({
            cameraContext: cameraContext
          });
        } catch (innerError) {
          this.handleCameraError('创建相机上下文失败，请重试');
        }
      }, 300); // 延迟300ms创建相机上下文
    } catch (error) {
      this.handleCameraError('创建相机上下文失败');
    }
  },

  /**
   * 相机错误事件
   */
  onCameraError(e) {
    // 清除可能存在的初始化超时计时器
    if (this.cameraInitTimeout) {
      clearTimeout(this.cameraInitTimeout);
      this.cameraInitTimeout = null;
    }

    // 处理特定类型的错误
    const errMsg = e.detail.errMsg || '';

    if (errMsg.includes('camera not authorized')) {
      this.handleCameraError('相机权限被拒绝，请在设置中允许使用相机');
    } else if (errMsg.includes('camera busy') || errMsg.includes('camera occupied')) {
      this.handleCameraError('相机被其他应用占用，请关闭其他使用相机的应用');
    } else if (errMsg.includes('camera service unavailable')) {
      this.handleCameraError('相机服务不可用，请重启手机后再试');
    } else {
      this.handleCameraError('相机发生错误: ' + errMsg);
    }

    // 关闭相机，避免持续错误
    this.setData({
      showCamera: false
    });
  },

  /**
   * 处理相机错误的统一方法
   */
  handleCameraError(message) {
    // 隐藏可能存在的加载提示
    wx.hideLoading();

    // 显示错误提示
    wx.showModal({
      title: '相机错误',
      content: message,
      confirmText: '我知道了',
      showCancel: false
    });
  },

  /**
   * 拍照
   */
  async takePhoto(e) {
    // 阻止事件冒泡
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }

    // 如果摄像头未打开，先打开摄像头
    if (!this.data.showCamera) {
      // 使用改进后的相机打开方法
      this.toggleCamera();
      return;
    }

    // 如果相机上下文不存在，可能是相机还未初始化完成
    if (!this.data.cameraContext) {
      wx.showToast({
        title: '相机正在准备中，请稍候',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    try {
      // 使用已保存的相机上下文
      const cameraContext = this.data.cameraContext;

      // 使用 loadingUtils 安全地管理加载状态
      await loadingUtils.withLoading(async () => {
        return new Promise((resolve, reject) => {
          // 设置拍照超时
          const photoTimeout = setTimeout(() => {
            reject(new Error('拍照操作超时，请检查相机权限或重启手机后再试'));
          }, 8000); // 8秒超时

          // 拍照 - 使用当前设置的闪光灯状态和分辨率
          cameraContext.takePhoto({
            quality: this.data.cameraResolution, // 使用设置的分辨率
            flash: this.data.cameraFlash, // 使用设置的闪光灯状态
            success: (res) => {
              // 清除超时计时器
              clearTimeout(photoTimeout);

              logUtils.log('Photo taken successfully', res);

              // 拍照成功，获取临时文件路径
              const tempImagePath = res.tempImagePath;
              logUtils.log('Temp image path', tempImagePath);

              // 使用新的权限API
              this.saveImageToAlbum(tempImagePath);
              resolve(res);
            },
            fail: (error) => {
              // 清除超时计时器
              clearTimeout(photoTimeout);

              logUtils.error('Failed to take photo', error);

              // 检查错误类型
              const errMsg = error.errMsg || '';

              if (errMsg.includes('camera not ready')) {
                reject(new Error('相机未准备好，请稍后再试'));
              } else if (errMsg.includes('camera busy')) {
                reject(new Error('相机正忙，请稍后再试'));
              } else {
                reject(new Error('拍照失败: ' + errMsg));
              }
            }
          });
        });
      }, {
        title: '拍照中...',
        mask: true
      });
    } catch (error) {
      logUtils.error('Exception in takePhoto', error);
      this.handleCameraError(error.message || '相机操作异常，请重新打开相机');

      // 重置相机状态
      this.setData({
        showCamera: false,
        cameraContext: null
      });
    }
  },

  /**
   * 保存图片到相册
   */
  saveImageToAlbum(filePath) {
    // 检查授权状态
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum']) {
          // 已授权，直接保存
          this.doSaveImageToAlbum(filePath);
        } else {
          // 未授权，请求授权
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.doSaveImageToAlbum(filePath);
            },
            fail: (error) => {
              logUtils.error('Photo album permission denied', error);

              // 用户拒绝授权，提示用户打开设置页面
              wx.showModal({
                title: '提示',
                content: '需要您授权保存照片到相册',
                confirmText: '去设置',
                success: (res) => {
                  if (res.confirm) {
                    wx.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting['scope.writePhotosAlbum']) {
                          // 用户在设置页面中授权了
                          this.doSaveImageToAlbum(filePath);
                        }
                      }
                    });
                  }
                }
              });
            }
          });
        }
      }
    });
  },

  /**
   * 执行保存图片到相册的操作
   */
  doSaveImageToAlbum(filePath) {
    wx.saveImageToPhotosAlbum({
      filePath: filePath,
      success: () => {
        logUtils.log('Photo saved to album');
        wx.showToast({
          title: '照片已保存到相册',
          icon: 'success'
        });
      },
      fail: (error) => {
        logUtils.error('Failed to save photo to album', error);
        wx.showToast({
          title: '保存照片失败: ' + error.errMsg,
          icon: 'none'
        });
      }
    });
  },

  /**
   * 开始直播
   */
  startLive() {
    // 如果摄像头未打开，先打开摄像头
    if (!this.data.showCamera) {
      this.setData({
        showCamera: true
      });
    }

    wx.showToast({
      title: '抖Live功能',
      icon: 'none'
    });
  },

  /**
   * 录像
   */
  recordVideo() {
    // 如果摄像头未打开，先打开摄像头
    if (!this.data.showCamera) {
      this.setData({
        showCamera: true
      });
      return;
    }

    // 使用已保存的相机上下文或创建新的
    const cameraContext = this.data.cameraContext || wx.createCameraContext('myCamera');

    // 开始录像
    cameraContext.startRecord({
      timeout: 10000, // 10秒自动结束
      success: () => {
        wx.showToast({
          title: '开始录像 (10秒)',
          icon: 'none'
        });

        // 显示录制中的提示
        loadingUtils.showLoading({
          title: '录制中...',
          mask: false
        });
      },
      fail: (error) => {
        logUtils.error('开始录像失败', error);
        // 确保隐藏加载提示
        loadingUtils.hideLoading();
        wx.showToast({
          title: '开始录像失败: ' + error.errMsg,
          icon: 'none'
        });
      },
      timeoutCallback: (res) => {
        // 录像达到指定时间自动结束时触发
        logUtils.log('录像自动结束', res);
        loadingUtils.hideLoading();

        // 录像成功，获取临时文件路径
        const tempVideoPath = res.tempVideoPath;
        logUtils.log('视频临时路径', tempVideoPath);

        // 保存视频到相册
        this.saveVideoToAlbum(tempVideoPath);
      }
    });
  },

  /**
   * 保存视频到相册
   */
  saveVideoToAlbum(filePath) {
    // 检查授权状态
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum']) {
          // 已授权，直接保存
          this.doSaveVideoToAlbum(filePath);
        } else {
          // 未授权，请求授权
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.doSaveVideoToAlbum(filePath);
            },
            fail: (error) => {
              logUtils.error('相册权限被拒绝', error);

              // 用户拒绝授权，提示用户打开设置页面
              wx.showModal({
                title: '提示',
                content: '需要您授权保存视频到相册',
                confirmText: '去设置',
                success: (res) => {
                  if (res.confirm) {
                    wx.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting['scope.writePhotosAlbum']) {
                          // 用户在设置页面中授权了
                          this.doSaveVideoToAlbum(filePath);
                        }
                      }
                    });
                  }
                }
              });
            }
          });
        }
      }
    });
  },

  /**
   * 执行保存视频到相册的操作
   */
  doSaveVideoToAlbum(filePath) {
    wx.saveVideoToPhotosAlbum({
      filePath: filePath,
      success: () => {
        logUtils.log('视频已保存到相册');
        wx.showToast({
          title: '视频已保存到相册',
          icon: 'success'
        });
      },
      fail: (error) => {
        logUtils.error('保存视频失败', error);
        wx.showToast({
          title: '保存视频失败: ' + error.errMsg,
          icon: 'none'
        });
      }
    });
  },

  /**
   * 延时拍摄
   */
  startLiveStream() {
    // 如果摄像头未打开，先打开摄像头
    if (!this.data.showCamera) {
      this.setData({
        showCamera: true
      });
    }

    wx.showToast({
      title: '延时拍摄功能',
      icon: 'none'
    });
  },

  /**
   * 切换前后摄像头
   */
  switchCamera() {
    // 如果摄像头未打开，先打开摄像头
    if (!this.data.showCamera) {
      this.setData({
        showCamera: true,
        devicePosition: 'front' // 直接打开前置摄像头
      });
      return;
    }

    // 切换前后摄像头
    this.setData({
      devicePosition: this.data.devicePosition === 'back' ? 'front' : 'back'
    });

    wx.showToast({
      title: this.data.devicePosition === 'front' ? '已切换到前置摄像头' : '已切换到后置摄像头',
      icon: 'none'
    });

    // 如果切换到前置摄像头，关闭闪光灯
    if (this.data.devicePosition === 'front' && this.data.cameraFlash !== 'off') {
      this.setData({
        cameraFlash: 'off'
      });
    }
  },

  /**
   * 切换闪光灯状态
   */
  toggleCameraFlash() {
    // 只有后置摄像头才能使用闪光灯
    if (this.data.devicePosition !== 'back') {
      wx.showToast({
        title: '前置摄像头不支持闪光灯',
        icon: 'none'
      });
      return;
    }

    // 切换闪光灯状态：off -> on -> auto -> off
    let newFlashState = 'off';

    switch (this.data.cameraFlash) {
      case 'off':
        newFlashState = 'on';
        break;
      case 'on':
        newFlashState = 'auto';
        break;
      case 'auto':
        newFlashState = 'off';
        break;
    }

    this.setData({
      cameraFlash: newFlashState
    });

    wx.showToast({
      title: newFlashState === 'off' ? '闪光灯已关闭' :
             newFlashState === 'on' ? '闪光灯已开启' : '闪光灯自动模式',
      icon: 'none'
    });
  },

  /**
   * 切换相机设置面板
   */
  toggleCameraSettings() {
    this.setData({
      showCameraSettings: !this.data.showCameraSettings
    });
  },

  /**
   * 关闭相机设置面板
   */
  closeCameraSettings() {
    this.setData({
      showCameraSettings: false
    });
  },

  /**
   * 设置相机分辨率
   */
  setCameraResolution(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      cameraResolution: value
    });

    wx.showToast({
      title: '分辨率已设置为' +
             (value === 'low' ? '低' :
              value === 'medium' ? '中' : '高'),
      icon: 'none'
    });
  },

  /**
   * 设置相机帧大小
   */
  setCameraFrameSize(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      cameraFrameSize: value
    });

    wx.showToast({
      title: '帧大小已设置为' +
             (value === 'small' ? '小' :
              value === 'medium' ? '中' : '大'),
      icon: 'none'
    });
  },

  /**
   * 切换相机显示状态
   */
  toggleCamera() {
    logUtils.log('toggleCamera called, current state', this.data.showCamera);

    // 获取当前相机状态的反状态
    const newCameraState = !this.data.showCamera;

    // 如果要打开相机，先检查权限
    if (newCameraState) {
      // 使用 loadingUtils 安全地管理加载状态
      loadingUtils.withLoading(async () => {
        return new Promise((resolve, reject) => {
          // 检查相机权限
          wx.getSetting({
            success: (res) => {
              if (res.authSetting['scope.camera'] === false) {
                // 用户曾经拒绝过相机权限
                reject(new Error('需要相机权限'));
                wx.showModal({
                  title: '需要相机权限',
                  content: '请在设置中允许使用相机',
                  confirmText: '去设置',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      wx.openSetting();
                    }
                  }
                });
                return;
              }

              // 请求相机权限
              wx.authorize({
                scope: 'scope.camera',
                success: () => {
                  this.openCamera();
                  resolve();
                },
                fail: (error) => {
                  logUtils.error('Camera authorization failed', error);
                  reject(new Error('相机权限被拒绝，请在设置中允许使用相机'));
                }
              });
            },
            fail: (error) => {
              logUtils.error('Failed to get settings', error);
              reject(new Error('无法获取权限设置'));
            }
          });
        });
      }, {
        title: '正在打开相机...',
        mask: true
      }).catch((error) => {
        this.handleCameraError(error.message);
      });
    } else {
      // 关闭相机
      this.closeCamera();
    }
  },

  /**
   * 打开相机
   */
  openCamera() {
    logUtils.log('Opening camera');

    // 设置相机初始化超时 - 减少超时时间，避免用户等待过长
    if (this.cameraInitTimeout) {
      clearTimeout(this.cameraInitTimeout);
    }

    this.cameraInitTimeout = setTimeout(() => {
      // 如果超时仍未初始化完成，则认为相机打开失败
      if (this.data.showCamera && !this.data.cameraContext) {
        logUtils.error('Camera initialization timeout');
        this.handleCameraError('相机初始化超时，请检查相机权限或重启手机后再试');

        // 关闭相机
        this.setData({
          showCamera: false
        });
      }
    }, 6000); // 减少为6秒超时，避免用户等待过长

    try {
      // 切换相机的显示状态
      this.setData({
        showCamera: true,
        // 默认使用前置摄像头
        devicePosition: 'front',
        // 关闭相机设置面板
        showCameraSettings: false
      }, () => {
        // 在状态更新后执行
        logUtils.log('Camera state updated to', this.data.showCamera);

        // 延迟创建相机上下文，避免在某些机型上出现的时序问题
        setTimeout(() => {
          try {
            const cameraContext = wx.createCameraContext('myCamera');
            this.setData({
              cameraContext: cameraContext
            });
            logUtils.log('Camera context created on camera open');

            // 显示提示
            wx.showToast({
              title: '已打开相机',
              icon: 'success'
            });
          } catch (error) {
            logUtils.error('Failed to create camera context (delayed)', error);
            this.handleCameraError('创建相机上下文失败，请重试');

            // 不要立即关闭相机，给用户一个重试的机会
            // 如果用户需要关闭，可以点击相机按钮关闭
          }
        }, 500); // 延迟500ms创建相机上下文
      });
    } catch (error) {
      logUtils.error('Failed to open camera', error);
      this.handleCameraError('打开相机失败，请重试');

      // 重置相机状态
      this.setData({
        showCamera: false
      });
    }
  },

  /**
   * 关闭相机
   */
  closeCamera() {
    logUtils.log('Closing camera');

    // 清除可能存在的初始化超时计时器
    if (this.cameraInitTimeout) {
      clearTimeout(this.cameraInitTimeout);
      this.cameraInitTimeout = null;
    }

    // 关闭相机
    this.setData({
      showCamera: false,
      showCameraSettings: false
    }, () => {
      console.log('Camera closed');

      // 显示提示
      wx.showToast({
        title: '已关闭相机',
        icon: 'none'
      });
    });
  },

  /**
   * 颜色选择器颜色变化 - 优化版本
   */
  onColorPickerChange(e) {
    const color = e.detail.color;
    const textColor = this.getTextColorForBackground(color);
    // 从选中的颜色中提取HSL值
    const hsl = this.hexToHSL(color);

    // 检查是否是自定义颜色（不在预设颜色列表中）
    const isCustomColor = !this.isPresetColor(color);

    // 批量更新数据，减少setData调用次数
    const updateData = {
      currentColor: color,
      textColor: textColor,
      hue: hsl.h,
      saturation: hsl.s,
      lightness: hsl.l
    };

    // 只有当颜色描述需要改变时才更新
    if (isCustomColor) {
      updateData.colorDescription = '自定义颜色';
    }

    this.throttledSetData(updateData);
  },

  /**
   * 颜色选择器确认 - 优化版本
   */
  onColorPickerConfirm(e) {
    const color = e.detail.color;
    const textColor = this.getTextColorForBackground(color);
    // 从选中的颜色中提取HSL值
    const hsl = this.hexToHSL(color);

    // 检查是否是自定义颜色（不在预设颜色列表中）
    const isCustomColor = !this.isPresetColor(color);

    // 批量更新数据，减少setData调用次数
    const updateData = {
      currentColor: color,
      textColor: textColor,
      hue: hsl.h,
      saturation: hsl.s,
      lightness: hsl.l,
      showColorPicker: false
    };

    // 只有当颜色描述需要改变时才更新
    if (isCustomColor) {
      updateData.colorDescription = '自定义颜色';
    }

    this.setData(updateData);
  },



  /**
   * HEX颜色转HSL
   */
  hexToHSL(hex) {
    // 移除#号
    hex = hex.replace(/^#/, '');

    // 解析RGB值
    const r = parseInt(hex.substring(0, 2), 16) / 255;
    const g = parseInt(hex.substring(2, 4), 16) / 255;
    const b = parseInt(hex.substring(4, 6), 16) / 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h, s, l = (max + min) / 2;

    if (max === min) {
      h = s = 0; // 灰色
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }

      h = Math.round(h * 60);
    }

    s = Math.round(s * 100);
    l = Math.round(l * 100);

    return { h, s, l };
  },



  /**
   * 处理饱和度变化 - 优化版本
   */
  onSaturationChange(e) {
    this.throttledSetData({
      saturation: e.detail.value
    });
    // 应用到当前选中的预设颜色
    this.throttledAdjustColor();
  },

  /**
   * 处理明度变化 - 优化版本
   */
  onLightnessChange(e) {
    this.throttledSetData({
      lightness: e.detail.value
    });
    // 应用到当前选中的预设颜色
    this.throttledAdjustColor();
  },

  /**
   * 调整预设颜色的色调、饱和度和明度 - 优化版本
   */
  adjustPresetColor() {
    // 使用当前的色调、饱和度和明度值
    const color = this.hslToHex(this.data.hue, this.data.saturation, this.data.lightness);
    const textColor = this.getTextColorForBackground(color);

    // 检查是否是自定义颜色（不在预设颜色列表中）
    const isCustomColor = !this.isPresetColor(color);

    // 批量更新数据，减少setData调用次数
    const updateData = {
      currentColor: color,
      textColor: textColor
    };

    // 只有当颜色描述需要改变时才更新
    if (isCustomColor && this.data.colorDescription !== '自定义颜色') {
      updateData.colorDescription = '自定义颜色';
    }

    this.setData(updateData);
  },

  /**
   * 检查颜色是否在预设颜色列表中
   */
  isPresetColor(color) {
    // 将颜色转换为大写以进行比较
    const upperColor = color.toUpperCase();
    // 检查颜色是否在预设颜色列表中
    return this.data.presetColors.some(preset => preset.color.toUpperCase() === upperColor);
  },

  /**
   * HSL颜色转HEX
   */
  hslToHex(h, s, l) {
    s /= 100;
    l /= 100;

    const c = (1 - Math.abs(2 * l - 1)) * s;
    const x = c * (1 - Math.abs((h / 60) % 2 - 1));
    const m = l - c / 2;

    let r, g, b;

    if (h >= 0 && h < 60) {
      r = c; g = x; b = 0;
    } else if (h >= 60 && h < 120) {
      r = x; g = c; b = 0;
    } else if (h >= 120 && h < 180) {
      r = 0; g = c; b = x;
    } else if (h >= 180 && h < 240) {
      r = 0; g = x; b = c;
    } else if (h >= 240 && h < 300) {
      r = x; g = 0; b = c;
    } else {
      r = c; g = 0; b = x;
    }

    r = Math.round((r + m) * 255).toString(16).padStart(2, '0');
    g = Math.round((g + m) * 255).toString(16).padStart(2, '0');
    b = Math.round((b + m) * 255).toString(16).padStart(2, '0');

    return `#${r}${g}${b}`.toUpperCase();
  },

  /**
   * 选择预设颜色 - 优化版本
   */
  selectPresetColor(e) {
    const { color, textColor, description, name } = e.currentTarget.dataset;

    // 从选中的颜色中提取HSL值
    const hsl = this.hexToHSL(color);

    // 批量更新所有相关数据，减少setData调用次数
    this.setData({
      currentColor: color,
      textColor: textColor,
      colorDescription: name + '，' + description,
      hue: hsl.h,
      saturation: hsl.s,
      lightness: hsl.l
    });
  },

  /**
   * 处理色调变化 - 优化版本
   */
  onHueChange(e) {
    const hue = e.detail.value;
    this.throttledSetData({
      hue: hue,
      currentHueColor: this.hslToHex(hue, 100, 50)
    });
    // 应用到当前颜色
    this.throttledAdjustColor();
  },

  /**
   * 通用滑块触摸开始处理函数
   */
  handleSliderStart(e, sliderType) {
    // 记录触摸开始状态
    this.setData({
      sliderTouching: true,
      currentSliderType: sliderType
    });

    // 立即处理触摸位置
    this.handleSliderMove(e, sliderType);
  },

  /**
   * 通用滑块移动处理函数 - 优化版本，提高触摸灵敏度
   */
  handleSliderMove(e, sliderType) {
    if (e.touches.length === 0) return;

    const touch = e.touches[0];

    // 使用小程序的SelectorQuery API获取准确的位置信息
    const query = wx.createSelectorQuery().in(this);
    query.select('.color-slider-container').boundingClientRect().exec((res) => {
      if (!res || !res[0]) {
        // 降级方案：使用估算的位置
        this.handleSliderMoveFallback(touch, sliderType);
        return;
      }

      const rect = res[0];
      const containerWidth = rect.width;

      // 计算滑块位置对应的值
      let position = touch.clientX - rect.left;
      position = Math.max(0, Math.min(position, containerWidth));

      // 计算百分比位置
      const percentage = position / containerWidth;

      this.updateSliderValue(percentage, sliderType);
    });
  },

  /**
   * 滑块移动处理的降级方案
   */
  handleSliderMoveFallback(touch, sliderType) {
    // 使用新的API获取屏幕宽度
    try {
      const windowInfo = wx.getWindowInfo();
      const screenWidth = windowInfo.windowWidth;
      const containerWidth = screenWidth - 120; // 减去左右边距

      // 假设滑块容器从左边距60rpx开始
      const containerLeft = 60;
      let position = touch.clientX - containerLeft;
      position = Math.max(0, Math.min(position, containerWidth));

      const percentage = position / containerWidth;
      this.updateSliderValue(percentage, sliderType);
    } catch (error) {
      // 如果新API也失败，使用默认值
      const defaultWidth = 375;
      const containerWidth = defaultWidth - 120;
      const containerLeft = 60;
      let position = touch.clientX - containerLeft;
      position = Math.max(0, Math.min(position, containerWidth));

      const percentage = position / containerWidth;
      this.updateSliderValue(percentage, sliderType);
    }
  },

  /**
   * 更新滑块数值
   */
  updateSliderValue(percentage, sliderType) {
    // 根据滑块类型计算对应的值和更新数据
    const updateData = {};
    let needsColorAdjust = false;
    let needsBrightnessSet = false;

    switch (sliderType) {
      case 'hue':
        const hue = Math.round(percentage * 360);
        updateData.hue = hue;
        updateData.currentHueColor = this.hslToHex(hue, 100, 50);
        needsColorAdjust = true;
        break;

      case 'saturation':
        updateData.saturation = Math.round(percentage * 100);
        needsColorAdjust = true;
        break;

      case 'lightness':
        updateData.lightness = Math.round(percentage * 100);
        needsColorAdjust = true;
        break;

      case 'brightness':
        const brightness = Math.round(10 + percentage * 90);
        updateData.screenBrightness = brightness;
        needsBrightnessSet = true;
        break;
    }

    // 使用节流的setData更新UI
    this.throttledSetData(updateData);

    // 根据需要执行额外操作
    if (needsColorAdjust) {
      this.throttledAdjustColor();
    }

    if (needsBrightnessSet) {
      // 屏幕亮度设置不需要节流，因为系统API本身有限制
      wx.setScreenBrightness({
        value: updateData.screenBrightness / 100,
        fail: () => {
          // 静默处理失败，不影响用户体验
        }
      });
    }
  },

  /**
   * 通用滑块触摸结束处理函数
   */
  handleSliderEnd(e, sliderType) {
    // 重置触摸状态
    this.setData({
      sliderTouching: false,
      currentSliderType: null
    });
  },

  /**
   * 色相滑块事件处理
   */
  onHueSliderStart(e) {
    this.handleSliderStart(e, 'hue');
  },

  onHueSliderMove(e) {
    this.handleSliderMove(e, 'hue');
  },

  onHueSliderEnd(e) {
    this.handleSliderEnd(e, 'hue');
  },

  /**
   * 饱和度滑块事件处理
   */
  onSaturationSliderStart(e) {
    this.handleSliderStart(e, 'saturation');
  },

  onSaturationSliderMove(e) {
    this.handleSliderMove(e, 'saturation');
  },

  onSaturationSliderEnd(e) {
    this.handleSliderEnd(e, 'saturation');
  },

  /**
   * 明度滑块事件处理
   */
  onLightnessSliderStart(e) {
    this.handleSliderStart(e, 'lightness');
  },

  onLightnessSliderMove(e) {
    this.handleSliderMove(e, 'lightness');
  },

  onLightnessSliderEnd(e) {
    this.handleSliderEnd(e, 'lightness');
  },

  /**
   * 屏幕亮度滑块事件处理
   */
  onBrightnessSliderStart(e) {
    this.handleSliderStart(e, 'brightness');
  },

  onBrightnessSliderMove(e) {
    this.handleSliderMove(e, 'brightness');
  },

  onBrightnessSliderEnd(e) {
    this.handleSliderEnd(e, 'brightness');
  },

  /**
   * 阻止事件冒泡的空方法
   */
  preventTap() {
    // 这个方法什么都不做，只是用来阻止事件冒泡
    console.log('Tap event prevented from bubbling');
  },

  /**
   * 根据背景色计算文字颜色
   * 如果背景色较深，返回白色；如果背景色较浅，返回黑色
   */
  getTextColorForBackground(backgroundColor) {
    // 移除#号
    const hex = backgroundColor.replace(/^#/, '');

    // 解析RGB值
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // 计算亮度 (HSP色彩模型)
    // 参考: http://alienryderflex.com/hsp.html
    const hsp = Math.sqrt(
      0.299 * (r * r) +
      0.587 * (g * g) +
      0.114 * (b * b)
    );

    // 如果亮度大于127.5，使用黑色文字；否则使用白色文字
    return hsp > 127.5 ? '#000000' : '#FFFFFF';
  }
})
