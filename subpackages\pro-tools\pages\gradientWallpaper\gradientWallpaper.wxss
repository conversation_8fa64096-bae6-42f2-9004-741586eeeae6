/* pages/gradientWallpaper/gradientWallpaper.wxss */
.page {
  height: 100vh;
  width: 100vw;
  position: relative;
  overflow: hidden;
}

.wallpaper-swiper {
  width: 100%;
  height: 100%;
}

.wallpaper-item {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  transition: all 0.3s ease;
  /* padding-top由JS动态计算并设置 */
}

/* 自定义导航栏 */
.custom-nav-container {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  /* 完全透明的背景 */
  background-color: transparent;
}

.status-bar {
  width: 100%;
}

.custom-nav {
  width: 100%;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 左侧区域 */
.nav-left {
  position: relative;
  padding-left: 16px;
  display: flex;
  align-items: center;
  height: 100%;
  width: 87px; /* 与右侧保持对称 */
  z-index: 10; /* 增加z-index，确保在标题之上 */
}

/* 返回按钮 */
.nav-back-btn {
  padding: 11px 25px 11px 0; /* 增加右侧padding，扩大点击区域 */
  margin: -11px 0 -11px -16px;
  display: flex;
  align-items: center;
  height: 100%;
  z-index: 10; /* 增加z-index，确保可点击 */
}

.nav-back-btn:active {
  opacity: 0.5;
}

.nav-back-arrow {
  width: 12px;
  height: 24px;
  -webkit-mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='24' viewBox='0 0 12 24'%3E  %3Cpath fill-opacity='.9' fill-rule='evenodd' d='M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 0 1 0-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z'/%3E%3C/svg%3E") no-repeat 50% 50%;
  mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='24' viewBox='0 0 12 24'%3E  %3Cpath fill-opacity='.9' fill-rule='evenodd' d='M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 0 1 0-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z'/%3E%3C/svg%3E") no-repeat 50% 50%;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: #ffffff;
  margin-left: 16px;
}

/* 中间标题区域 */
.nav-center {
  font-size: 17px;
  text-align: center;
  position: absolute;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  z-index: 1;
}

.nav-title {
  font-size: 17px;
  font-weight: 500;
  color: #ffffff;
  /* 移除文字阴影 */
}

/* 右侧区域 */
.nav-right {
  width: 87px;
  height: 100%;
}

.wallpaper-info {
  padding: 40rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
  color: #ffffff;
  position: relative;
  z-index: 10;
  margin-top: auto; /* 将信息区域推到底部 */
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* 左对齐 */
  justify-content: flex-start; /* 顶部对齐，使内容上移 */
  height: 50%; /* 进一步增加高度，使内容可以更上移 */
  padding-top: 80rpx; /* 进一步增加顶部内边距，使内容位置更高 */
}

/* 名称区域放在左下靠上方 */
.wallpaper-title {
  margin-bottom: 8rpx; /* 减小底部间距 */
  text-align: left;
  max-width: 80%; /* 限制宽度 */
}

.wallpaper-name {
  font-size: 44rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.wallpaper-english-name {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 24rpx; /* 增加底部间距，使颜色点下移 */
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 左下靠上布局容器 */
.left-bottom-top-layout {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 10rpx; /* 减小底部间距 */
  width: 100%;
  position: relative;
  padding-bottom: 0; /* 移除底部内边距 */
}

.wallpaper-colors {
  display: flex;
  gap: 0; /* 移除间距，因为我们现在使用箭头作为间隔 */
  margin-bottom: 20rpx; /* 增加底部间距 */
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center; /* 垂直居中对齐 */
  margin-left: 0; /* 移除左边距，与名称对齐 */
  max-width: 90%; /* 增加宽度，为箭头留出空间 */
}

.color-item {
  display: flex;
  align-items: center;
  justify-content: center;
  /* 移除背景色和模糊效果 */
  padding: 4rpx; /* 进一步减小内边距 */
  margin-bottom: 2rpx; /* 进一步减小底部间距 */
  margin-right: 0; /* 移除右侧间距，因为我们现在使用箭头作为间隔 */
}

.color-dot {
  width: 28rpx; /* 调小圆圈图标 */
  height: 28rpx; /* 调小圆圈图标 */
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 色值文本样式已移除 */

/* 箭头样式 */
.color-arrow {
  font-size: 24rpx; /* 调小字体大小 */
  color: #ffffff;
  margin: 0 6rpx; /* 减小左右间距 */
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold; /* 加粗显示 */
}

/* CSS样式相关样式已移除 */

.controls {
  position: absolute;
  bottom: 40rpx;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 40rpx;
  z-index: 20;
  pointer-events: none; /* 让控制区域不阻挡滑动 */
}

.control-buttons, .control-btn {
  pointer-events: auto; /* 恢复按钮的点击事件 */
}

/* 上下滑动提示相关样式已移除 */

.control-buttons {
  display: flex;
  width: 100%;
  gap: 24rpx;
}

.control-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  border: none;
  color: #ffffff;
}

.refresh-btn {
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.save-btn {
  background-color: rgba(7, 193, 96, 0.8);
  backdrop-filter: blur(10px);
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.refresh-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z'/%3E%3C/svg%3E");
}

.save-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zm-6 .67l2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2v9.67z'/%3E%3C/svg%3E");
}

.save-success {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  background-color: rgba(0, 0, 0, 0.7);
  padding: 30rpx 40rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: 100;
}

.save-success.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.success-icon {
  width: 80rpx;
  height: 80rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2307c160'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  margin-bottom: 16rpx;
}

.success-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
}

/* 隐藏的canvas */
.hidden-canvas {
  position: fixed;
  left: -9999rpx;
  top: -9999rpx;
  width: 100vw;
  height: 100vh;
}
