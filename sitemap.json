{"rules": [{"action": "allow", "page": "pages/index/index", "priority": 10, "index": "true"}, {"action": "allow", "page": "pages/colorQuery/colorQuery", "priority": 9, "index": "true"}, {"action": "allow", "page": "pages/colorDetail/colorDetail", "params": ["id"], "matching": "exact", "priority": 9, "index": "true"}, {"action": "allow", "page": "subpackages/pro-tools/pages/colorPalette/colorPalette", "priority": 8, "index": "true"}, {"action": "allow", "page": "subpackages/pro-tools/pages/contrastChecker/contrastChecker", "priority": 7, "index": "true"}, {"action": "allow", "page": "subpackages/pro-tools/pages/colorblindSimulator/colorblindSimulator", "priority": 7, "index": "true"}, {"action": "allow", "page": "subpackages/pro-tools/pages/gradientGenerator/gradientGenerator", "priority": 8, "index": "true"}, {"action": "allow", "page": "subpackages/pro-tools/pages/gradientWallpaper/gradientWallpaper", "priority": 8, "index": "true"}, {"action": "allow", "page": "subpackages/pro-tools/pages/colorConverter/colorConverter", "priority": 6, "index": "true"}, {"action": "allow", "page": "subpackages/pro-tools/pages/toneGenerator/toneGenerator", "priority": 6, "index": "true"}, {"action": "allow", "page": "subpackages/pro-tools/pages/ambientLight/ambientLight", "priority": 6, "index": "true"}, {"action": "allow", "page": "subpackages/pro-tools/pages/imageColorPicker/imageColorPicker", "priority": 6, "index": "true"}, {"action": "allow", "page": "subpackages/application/pages/clothingColorTool/clothingColorTool", "priority": 7, "index": "true"}, {"action": "allow", "page": "subpackages/application/pages/about/about", "priority": 5, "index": "true"}, {"action": "allow", "page": "subpackages/application/pages/skinToneTest/skinToneTest", "priority": 7, "index": "true"}, {"action": "disallow", "page": "pages/template/template"}, {"action": "disallow", "page": "pages/colorPicker/colorPicker"}, {"action": "disallow", "page": "pages/preview/preview"}, {"action": "disallow", "page": "subpackages/application/pages/clothingPreview/clothingPreview"}, {"action": "disallow", "page": "subpackages/application/pages/skinToneReport/skinToneReport"}, {"action": "disallow", "page": "*"}]}