/* pages/colorPalette/colorPalette.wxss */
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f7f7f7;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
  flex: 1;
  padding: 24rpx 20rpx;
  overflow-y: auto;
  /* 顶部内边距通过内联样式动态设置 */
  box-sizing: border-box;
}

/* 通用部分样式 */
.section {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  width: 100%;
  transition: all 0.2s ease;
  overflow: hidden;
}

.section:active {
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.03);
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #191919;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background-color: #07c160;
  border-radius: 3rpx;
}

/* 基础颜色选择部分 */
.base-color-section {
  margin-bottom: 24rpx;
  padding: 16rpx 20rpx 20rpx; /* 底部内边距20rpx */
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  overflow: hidden; /* 确保内容不会溢出 */
}

/* 色轮可视化部分 */
.color-wheel-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 0;
  margin-top: 10rpx;
}

.base-color-container {
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  padding: 4rpx 0 0;
  flex-wrap: nowrap;
  justify-content: space-between;
  gap: 0;
  overflow: hidden;
}

.color-preview {
  width: 63%;
  height: 134rpx;
  border-radius: 10rpx;
  margin-right: 0;
  border: none;
  flex: 0.63;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 0;
  margin-top: -2rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;

}

.color-preview:active {
  transform: scale(0.98);
  opacity: 0.95;
}

/* 色块中的16进制色值 */
.color-hex-value {
  font-size: 28rpx;
  font-family: 'Courier New', monospace;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  text-align: center;
  max-width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0.5rpx;
  font-weight: 500;
  /* 文字颜色通过内联样式动态设置 */
  /* 半透明背景，使文字更易读 */
  background-color: rgba(0, 0, 0, 0.15);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

/* 右侧上下并列的按钮容器 */
.color-actions-column {
  width: 38%;
  flex: 0.38;
  display: flex;
  flex-direction: column;
  gap: 14rpx;
  min-width: 180rpx;
  max-width: 220rpx;
  margin-left: 0;
  margin-bottom: 30rpx;
}

/* 按钮包装器 */
.btn-wrapper {
  width: 100%;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: -1rpx;
  box-sizing: border-box;
  position: relative;
}

.custom-btn {
  width: 100%;
  font-size: 22rpx;
  padding: 0 4rpx;
  background-color: #f5f5f5;
  color: #333333;
  border-radius: 6rpx;
  border: none;
  line-height: 60rpx;
  height: 60rpx;
  transition: all 0.2s ease;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  font-weight: 500;
  position: relative;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  letter-spacing: -0.8rpx;
  text-align: center;
  margin: 0;
  display: block;
  box-sizing: border-box;
  cursor: pointer;
}

/* 随机颜色按钮 */
.random-btn {
  background-color: #f0f0f0;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 选择颜色按钮 */
.picker-btn {
  background-color: #07c160;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.3);
}

.custom-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 历史记录样式 */
.color-history {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #eeeeee;
  width: 100%;
}

.history-title {
  font-size: 24rpx;
  color: #888888;
  margin-bottom: 12rpx;
}

.history-scroll {
  width: 100%;
  white-space: nowrap;
  overflow-x: auto;
}

.history-list {
  display: inline-flex;
  padding: 8rpx 0;
}

.history-item {
  display: inline-block;
  width: 56rpx;
  height: 56rpx;
  margin-right: 12rpx;
  border-radius: 4rpx;
  border: 1rpx solid #eeeeee;
  transition: transform 0.2s ease;
  flex-shrink: 0;
}

.history-item:active {
  transform: scale(0.92);
}

/* 合并的配色方案和色轮可视化模块 */
.combined-section {
  margin-bottom: 24rpx;
  padding: 28rpx 24rpx 36rpx; /* 进一步增加上下内边距 */
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 布局容器 */
.combined-layout {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-top: 25rpx; /* 增加顶部边距 */
}

/* 色轮说明文字 */
.wheel-description {
  width: 100%;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
}

.description-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  text-align: center;
  display: block;
}

/* 颜色预览和色轮容器 */
.color-preview-wheel-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-top: 25rpx;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
  min-height: 600rpx; /* 进一步增加最小高度，提供更多空间 */
  padding: 15rpx 0 20rpx; /* 进一步增加上下内边距 */
  position: relative;
}

/* 颜色预览区域 */
.color-preview-area {
  width: 94%; /* 增加宽度，减少两侧空白 */
  display: flex;
  flex-direction: column;
  min-width: 0;
  position: relative;
  z-index: 10;
  background-color: #fff;
  border-radius: 4rpx;
  padding: 16rpx 12rpx 18rpx; /* 减少上下内边距 */
  box-sizing: border-box;
  margin: 10rpx auto 0; /* 增加顶部外边距 */
}

/* 色轮可视化 */
.color-wheel-wrapper {
  width: 320rpx; /* 进一步增加宽度 */
  height: 320rpx; /* 进一步增加高度 */
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 5;
  overflow: visible;
  border-radius: 50%;
  padding: 0;
  box-sizing: border-box;
  margin: 25rpx 0 35rpx; /* 保持上下边距 */
}

/* 配色方案类型选择部分 */
.palette-type-container {
  width: 100%;
  padding: 10rpx 0;
  margin-bottom: 30rpx; /* 保持底部边距 */
  margin-top: 20rpx; /* 保持顶部边距 */
  display: flex;
  flex-direction: column;
  gap: 14rpx; /* 调整行间距为14rpx */
  background-color: #fff;
  position: relative;
  z-index: 10;
}

.palette-type-row {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 0; /* 移除底部边距，使用gap控制间距 */
}

.palette-type-row:last-child {
  margin-bottom: 0;
}

.palette-type-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0; /* 移除内边距，使用固定高度 */
  background-color: #f5f5f5;
  border-radius: 6rpx; /* 增大圆角 */
  transition: all 0.2s ease;
  text-align: center;
  height: 60rpx; /* 保持增大的高度 */
  width: 32%;
  box-sizing: border-box;
  flex-shrink: 0;
  position: relative;
  z-index: 20;
  border: 1rpx solid transparent;
}

.palette-type-text {
  font-size: 28rpx; /* 进一步增大字体大小，与更高的按钮协调 */
  color: #333333;
  line-height: 1;
  display: block;
  width: 100%;
  font-weight: 400;
}

.palette-type-item.active {
  background-color: #07c160;
  border-color: #07c160;
  position: relative;
}

.palette-type-item.active .palette-type-text {
  color: #ffffff;
  font-weight: 500;
}

.palette-type-item:active {
  opacity: 0.85;
}

/* 色轮可视化部分 - 已合并到上面的样式中 */

/* 配色方案名称样式 */
.palette-type-name {
  margin-top: 5rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  background-color: #f5f5f5;
  padding: 10rpx 24rpx;
  border-radius: 6rpx;
  display: inline-block;
  box-shadow: none;
  margin-bottom: 20rpx;
  width: auto;
  border: 1rpx solid #eee;
  position: relative;
  z-index: 10;
}

/* 颜色预览条样式 */
.color-preview-bar {
  width: 100%;
  height: 90rpx; /* 进一步减少高度 */
  display: flex;
  margin-top: 0;
  border-radius: 4rpx;
  overflow: hidden;
  position: relative;
  margin-bottom: 14rpx;
  z-index: 5;
}

.color-preview-segment {
  height: 100%;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  min-width: 60rpx; /* 确保每个色块有最小宽度 */
  margin: 0 2rpx; /* 添加小间距代替边框 */
}

.color-preview-segment:first-child {
  margin-left: 0;
}

.color-preview-segment:last-child {
  margin-right: 0;
}

.color-preview-segment:active {
  opacity: 0.9;
}

.segment-copy-hint {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 500;
  opacity: 0;
  transition: all 0.2s ease;
}

.segment-copy-hint.show {
  opacity: 1;
}

/* 颜色值显示样式 */
.color-preview-values {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx; /* 减少顶部边距 */
  flex-wrap: nowrap;
  z-index: 5;
  position: relative;
}

.color-preview-value {
  font-size: 24rpx;
  color: #333;
  font-family: 'Courier New', monospace;
  text-align: center;
  padding: 8rpx 0; /* 保持减少的内边距 */
  letter-spacing: 0.5rpx;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  background-color: transparent; /* 移除背景底色 */
  margin: 0 2rpx;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 40rpx; /* 保持减少的高度 */
  line-height: 40rpx; /* 保持减少的行高 */
}

.color-preview-value:first-child {
  margin-left: 0;
}

.color-preview-value:last-child {
  margin-right: 0;
}

.color-preview-value:active {
  opacity: 0.7; /* 使用透明度代替背景色变化 */
}

.color-value-text {
  display: block;
}

.copy-hint {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(7, 193, 96, 0.85); /* 稍微调整透明度 */
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx; /* 减小字体大小 */
  font-weight: 500;
  opacity: 0;
  transition: all 0.2s ease;
}

.copy-hint.show {
  opacity: 1;
}

/* 相关颜色说明 */
.related-colors-info {
  width: 100%;
  margin: 0 0 10rpx; /* 减少底部边距 */
  box-sizing: border-box;
  padding: 6rpx 0; /* 减少内边距 */
  position: relative;
  z-index: 5;
}

.related-colors-text {
  font-size: 20rpx; /* 减小字体大小 */
  color: #666;
  line-height: 1.3; /* 减少行高 */
  text-align: center;
  display: block;
  word-break: break-word;
  white-space: normal;
}

/* 颜色选择器弹窗 */
.color-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.color-picker-container {
  width: 90%;
  max-width: 650rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
  animation: scaleIn 0.2s ease;
  transform-origin: center;
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.color-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 28rpx;
  border-bottom: 1rpx solid #eeeeee;
  background-color: #fafafa;
}

.color-picker-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #191919;
}

.color-picker-close {
  font-size: 36rpx;
  color: #666666;
  line-height: 1;
  padding: 10rpx;
  margin: -10rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.color-picker-close:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.color-picker-placeholder {
  padding: 48rpx 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.color-picker-placeholder text {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 32rpx;
}

.color-picker-buttons {
  display: flex;
  width: 100%;
  gap: 20rpx;
  padding: 0 28rpx 28rpx;
}

.color-picker-btn {
  flex: 1;
  font-size: 28rpx;
  padding: 16rpx 0;
  border-radius: 10rpx;
  border: none;
  transition: all 0.2s ease;
  font-weight: 500;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.color-picker-btn.cancel {
  background-color: #f5f5f5;
  color: #333333;
}

.color-picker-btn.confirm {
  background-color: #07c160;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.2);
}

.color-picker-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}
