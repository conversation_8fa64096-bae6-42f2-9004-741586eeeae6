.container {
  display: flex;
  flex-direction: column;
  background-color: #fafafa;
  min-height: 100vh;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #333;
}

.preview-section {
  width: 100%;
  height: 820rpx; /* 增加高度 */
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;
  position: relative;
  overflow: hidden;
  border-radius: 0;
  box-shadow: none;
}

/* 固定位置的预览区域 */
.preview-section.fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 5; /* 降低z-index，从100改为5，确保不会遮挡控制区域 */
  margin-bottom: 0;
}

/* 预览区域的占位，确保滚动内容正确定位 */
.preview-placeholder {
  width: 100%;
  height: 820rpx; /* 恢复原来的高度，从800rpx改为820rpx */
  margin-bottom: 10rpx; /* 恢复底部间距，从0改为20rpx */
}

/* 模板选择器样式 */
.template-selector {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: auto;
  min-width: 160rpx;
}

.picker-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 24rpx; /* 减小字体大小，从26rpx改为24rpx */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  min-width: 140rpx; /* 添加最小宽度，确保有足够的空间 */
  margin-bottom: 10rpx;
}



.picker-arrow {
  margin-left: 10rpx;
  font-size: 20rpx;
  color: #666;
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.svg-image {
  width: 70%; /* 调整宽度 */
  height: 95%; /* 增加高度 */
  object-fit: contain;
  display: block; /* 确保正确显示 */
}

/* 滑动提示指引样式 */
.swipe-guide {
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 8rpx 16rpx; /* 减小内边距，从12rpx 24rpx改为8rpx 16rpx */
  border-radius: 30rpx; /* 减小圆角，从40rpx改为30rpx */
  opacity: 0.8; /* 降低不透明度，从0.9改为0.8 */
  transition: opacity 0.5s ease, transform 0.5s ease;
  animation: fadeInUp 0.8s ease forwards, pulse 2s infinite;
}

.swipe-guide.hide {
  opacity: 0;
  transform: translate(-50%, 40rpx); /* 调整为与动画初始位置一致 */
  pointer-events: none;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate(-50%, 40rpx); /* 调整初始位置，使动画更协调 */
  }
  to {
    opacity: 0.9;
    transform: translate(-50%, 0);
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, 0) scale(1);
  }
  50% {
    transform: translate(-50%, 0) scale(1.05);
  }
  100% {
    transform: translate(-50%, 0) scale(1);
  }
}

.swipe-text {
  color: #fff;
  font-size: 20rpx; /* 减小字体大小，从24rpx改为20rpx */
  margin: 0 12rpx; /* 减小左右边距，从16rpx改为12rpx */
}

.swipe-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx; /* 减小宽度，从40rpx改为32rpx */
  height: 32rpx; /* 减小高度，从40rpx改为32rpx */
}

.arrow {
  color: #fff;
  font-size: 24rpx; /* 减小字体大小，从28rpx改为24rpx */
  animation: arrowMove 1.5s infinite;
}

.swipe-icon.left .arrow {
  animation: arrowMoveLeft 1.5s infinite;
}

.swipe-icon.right .arrow {
  animation: arrowMoveRight 1.5s infinite;
}

@keyframes arrowMoveLeft {
  0%, 100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-6rpx);
  }
}

@keyframes arrowMoveRight {
  0%, 100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(6rpx);
  }
}

.controls-section {
  padding: 10rpx 20rpx 120rpx; /* 增加底部内边距，确保内容不被固定按钮遮挡 */
  background-color: #fafafa;
  border-top: none;
  box-sizing: border-box;
  position: relative; /* 添加相对定位 */
  z-index: 10; /* 确保控制区域在上层 */
}

/* 颜色选择区域 */
.color-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.color-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 10rpx 10rpx; /* 恢复顶部内边距，从10rpx改为20rpx */
  background-color: #f9f9f9;
  border-radius: 0;
  box-sizing: border-box;
}

/* 删除不必要的样式 */

.color-grid .color-block {
  width: 48%;
  margin-bottom: 60rpx; /* 保持底部间距 */
  position: relative;
  background-color: transparent;
  box-sizing: border-box;
  border: none;
  padding: 0 5rpx; /* 添加少量水平内边距 */
}

.label-container {
  margin-bottom: 5rpx; /* 进一步减少底部间距，从10rpx改为5rpx */
  position: relative;
  z-index: 2;
  height: 36rpx; /* 减小高度，从40rpx改为36rpx */
}

.color-label {
  font-size: 24rpx;
  color: #555;
  display: block;
  font-weight: 500;
  letter-spacing: 1rpx;
  line-height: 36rpx;
  background-color: #fafafa; /* 添加背景色，确保文字可见 */
  position: relative; /* 添加相对定位 */
  z-index: 15; /* 确保标签在最上层 */
}

.color-selector {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 10rpx 15rpx; /* 减小上下内边距，从12rpx改为10rpx */
  border-radius: 4rpx;
  transition: all 0.2s ease;
  border: 1rpx solid #eeeeee;
  position: relative;
  z-index: 1;
  height: 52rpx; /* 减小高度，从56rpx改为52rpx */
  box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.03);
  margin-top: 0;
}

.color-selector:active {
  transform: scale(0.98);
  background-color: #fafafa;
}

.color-dot {
  width: 40rpx; /* 增加宽度，从28rpx改为40rpx */
  height: 40rpx; /* 增加高度，从28rpx改为40rpx */
  border-radius: 4rpx;
  margin-right: 12rpx; /* 增加右边距，从10rpx改为12rpx */
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.color-code {
  font-size: 22rpx; /* 增加字体大小，从20rpx改为22rpx */
  color: #888; /* 调整颜色，使其更加明显 */
  font-family: monospace;
  flex: 1;
  text-align: right;
  letter-spacing: 0.5rpx;
}

/* 模板选择区域 */
.template-row {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 30rpx;
  color: #333;
  margin: 10rpx 0 30rpx;
  display: block;
  font-weight: 500;
  letter-spacing: 1rpx;
}

.template-options {
  display: flex;
  flex-wrap: wrap;
}

.template-btn {
  padding: 16rpx 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-right: 20rpx;
  margin-bottom: 16rpx;
  font-size: 26rpx;
  color: #666;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.template-btn.active {
  background-color: #4a90e2;
  color: #fff;
}

/* 底部固定按钮容器 */
.btn-container {
  padding: 20rpx 30rpx 50rpx; /* 增加底部内边距，确保白色背景延伸到屏幕底部 */
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  bottom: 0; /* 固定在屏幕底部 */
  left: 0;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: row; /* 水平排列 */
  gap: 20rpx; /* 按钮间距 */
}

/* 按钮样式 */
.action-btn {
  flex: 1; /* 使用flex布局，按钮平分空间 */
  padding: 18rpx 0;
  border-radius: 8rpx; /* 统一圆角 */
  font-size: 30rpx; /* 统一字体大小 */
  text-align: center;
  transition: all 0.2s ease;
  font-weight: 500;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

.action-btn.reset {
  background-color: #f5f5f5;
  color: #333333;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.action-btn.random {
  background-color: #1aad19; /* 使用微信绿色 */
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(26, 173, 25, 0.15); /* 统一阴影 */
}

/* 颜色选择器弹窗 */
.color-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.color-picker-container {
  width: 90%;
  max-width: 650rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
  animation: scaleIn 0.2s ease;
  transform-origin: center;
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.color-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 28rpx;
  border-bottom: 1rpx solid #eeeeee;
  background-color: #fafafa;
}

.color-picker-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #191919;
}

.color-picker-close {
  font-size: 36rpx;
  color: #666666;
  line-height: 1;
  padding: 10rpx;
  margin: -10rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.color-picker-close:active {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 模板选择按钮下方的提示文字样式 */
.coming-soon-tip {
  font-size: 20rpx;
  color: #888;
  margin-top: 8rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* 提示文字每行样式 */
.tip-line {
  line-height: 1.2;
}

/* 第一行样式 */
.tip-line:first-child {
  margin-bottom: 2rpx;
}



