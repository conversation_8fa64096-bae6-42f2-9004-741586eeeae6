// pages/clothingPreview/clothingPreview.js
Page({
  data: {
    svgImageSrc: '', // SVG图片源
    isLoading: true, // 加载状态
    loadError: false, // 加载错误状态
    startX: 0 // 触摸开始位置，用于滑动返回
  },

  // 上次点击时间，用于检测双击
  lastTapTime: 0,

  onLoad: function(options) {
    // 初始化上次点击时间
    this.lastTapTime = 0;

    try {
      let imageSrc = '';

      // 检查是否使用全局数据
      if (options.useGlobal && getApp().globalData && getApp().globalData.tempSvgImageSrc) {
        // 从全局数据中获取图片源
        imageSrc = getApp().globalData.tempSvgImageSrc;
        console.log('从全局数据获取图片源');
      }
      // 兼容旧版本，从URL参数中获取图片源
      else if (options.src) {
        // 解码URL编码的图片源
        imageSrc = decodeURIComponent(options.src);
        console.log('从URL参数获取图片源');
      }

      if (imageSrc) {
        console.log('接收到的图片源类型:', imageSrc.substring(0, 30) + '...');

        // 设置图片源
        this.setData({
          svgImageSrc: imageSrc,
          isLoading: true,
          loadError: false
        });

        // 延迟一小段时间后隐藏加载状态，确保图片有时间渲染
        setTimeout(() => {
          this.setData({
            isLoading: false
          });
        }, 500);
      } else {
        console.error('未找到有效的图片源');
        this.setData({
          loadError: true,
          isLoading: false
        });

        wx.showToast({
          title: '缺少图片源',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载图片源失败:', error);
      this.setData({
        loadError: true,
        isLoading: false
      });

      wx.showToast({
        title: '图片加载失败',
        icon: 'none'
      });
    }
  },

  // 不再需要预加载图片函数

  // 图片加载完成事件
  onImageLoad: function() {
    console.log('图片加载完成');
    this.setData({
      isLoading: false
    });
  },

  // 图片加载失败事件
  onImageError: function(e) {
    console.error('图片加载失败:', e.detail);

    // 尝试使用备用方法显示
    setTimeout(() => {
      this.setData({
        isLoading: false
      });
    }, 500);
  },

  // 返回上一页
  goBack: function() {
    console.log('返回上一页');
    wx.navigateBack({
      delta: 1,
      fail: (err) => {
        console.error('返回失败:', err);
        // 如果返回失败，尝试重定向到首页
        wx.switchTab({
          url: '/pages/index/index',
          fail: (switchErr) => {
            console.error('切换到首页失败:', switchErr);
          }
        });
      }
    });
  },

  // 触摸开始事件
  touchStart: function(e) {
    // 记录触摸开始位置
    this.setData({
      startX: e.touches[0].clientX
    });
  },

  // 触摸结束事件
  touchEnd: function(e) {
    const endX = e.changedTouches[0].clientX;
    const distance = endX - this.data.startX;

    // 如果向右滑动超过100px，触发返回上一页
    if (distance > 100) {
      this.goBack();
    }
  },

  // 双击事件处理
  doubleTap: function() {
    // 获取当前时间
    const now = new Date().getTime();

    // 如果距离上次点击小于300ms，视为双击
    if (now - this.lastTapTime < 300) {
      this.goBack();
    }

    // 更新上次点击时间
    this.lastTapTime = now;
  }
})
