/* pages/colorblindSimulator/colorblindSimulator.wxss */
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
}

.container {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
  box-sizing: border-box;
}

/* 通用部分样式 */
.section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 16rpx; /* 减少内边距 */
  margin-bottom: 16rpx; /* 减少底部边距 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  width: 100%;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #191919;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background-color: #07c160;
  border-radius: 3rpx;
}

/* 颜色输入部分 */
.input-section {
  padding-bottom: 24rpx; /* 增加底部内边距 */
}
.base-color-container {
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  padding: 4rpx 0 0;
  flex-wrap: nowrap;
  justify-content: space-between;
  gap: 0;
  overflow: hidden;
  margin-bottom: 0; /* 移除底部边距，因为已经没有颜色信息显示了 */
}

.color-preview {
  width: 63%;
  height: 134rpx;
  border-radius: 10rpx;
  margin-right: 0;
  border: none;
  flex: 0.63;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 0;
  margin-top: -2rpx;
  box-sizing: border-box;
  margin-bottom: 0; /* 移除底部边距 */
}

.color-preview:active {
  transform: scale(0.98);
  opacity: 0.95;
}

/* 色块中的16进制色值 */
.color-hex-value {
  font-size: 28rpx;
  font-family: 'Courier New', monospace;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  text-align: center;
  max-width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0.5rpx;
  font-weight: 500;
  /* 文字颜色通过内联样式动态设置 */
  /* 半透明背景，使文字更易读 */
  background-color: rgba(0, 0, 0, 0.15);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

/* 右侧上下并列的按钮容器 */
.color-actions-column {
  width: 38%;
  flex: 0.38;
  display: flex;
  flex-direction: column;
  gap: 14rpx;
  min-width: 180rpx;
  max-width: 220rpx;
  margin-left: 0;
  margin-bottom: 0; /* 移除底部边距 */
}

/* 按钮包装器 */
.btn-wrapper {
  width: 100%;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: -1rpx;
  box-sizing: border-box;
  position: relative;
}

.custom-btn {
  width: 100%;
  font-size: 22rpx;
  padding: 0 4rpx;
  background-color: #f5f5f5;
  color: #333333;
  border-radius: 6rpx;
  border: none;
  line-height: 60rpx;
  height: 60rpx;
  transition: all 0.2s ease;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  font-weight: 500;
  position: relative;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  letter-spacing: -0.8rpx;
  text-align: center;
  margin: 0;
  display: block;
  box-sizing: border-box;
  cursor: pointer;
}

/* 随机颜色按钮 */
.random-btn {
  background-color: #f0f0f0;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 选择颜色按钮 */
.picker-btn {
  background-color: #07c160;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.3);
}

.custom-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 历史记录样式 */
.color-history {
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  width: 100%;
}

.history-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.history-scroll {
  width: 100%;
  white-space: nowrap;
  overflow-x: auto;
}

.history-list {
  display: inline-flex;
  padding: 10rpx 0;
}

.history-item {
  display: inline-block;
  width: 60rpx;
  height: 60rpx;
  margin-right: 15rpx;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s;
  flex-shrink: 0;
}

.history-item:active {
  transform: scale(0.95);
}

/* 颜色信息部分 */
.color-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  width: 80rpx;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.info-value {
  flex: 1;
  font-size: 24rpx;
  color: #666;
  font-family: monospace;
}

/* 移除了色盲类型选择部分的样式 */

/* 模拟结果部分 */
.result-section {
  padding-bottom: 20rpx;
  padding-left: 8rpx;
  padding-right: 8rpx;
}

.result-intro {
  font-size: 22rpx;
  color: #888;
  margin-bottom: 12rpx;
  text-align: center;
  padding: 0 10rpx;
}

.result-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 改为3列 */
  gap: 12rpx; /* 减小间距 */
  width: 100%;
}

.result-item {
  display: flex;
  flex-direction: column;
  padding: 10rpx; /* 减小内边距 */
  background-color: #ffffff;
  border-radius: 10rpx;
  box-sizing: border-box;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
}

.result-item-normal {
  background-color: #f8fff9; /* 轻微的绿色背景，表示原始颜色 */
  border: 1rpx solid rgba(7, 193, 96, 0.1);
}

.result-color-container {
  position: relative;
  width: 100%;
  height: 70rpx; /* 减小高度 */
  margin-bottom: 8rpx; /* 减小底部边距 */
  border-radius: 6rpx;
  overflow: hidden;
}

.result-color {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
  box-shadow: inset 0 0 0 1rpx rgba(0, 0, 0, 0.05);
}

.result-info {
  display: flex;
  flex-direction: column;
  padding: 0 2rpx;
}

.result-name {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4rpx;
}

.result-name-text {
  font-size: 22rpx; /* 减小字体 */
  color: #333;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 85%; /* 限制宽度，防止溢出 */
}

.result-name-badge {
  font-size: 18rpx; /* 减小字体 */
  color: #07c160;
  background-color: rgba(7, 193, 96, 0.1);
  padding: 0 6rpx;
  border-radius: 8rpx;
  font-weight: normal;
  flex-shrink: 0;
}

.result-value {
  font-size: 20rpx; /* 减小字体 */
  color: #666;
  font-family: monospace;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0.02);
  padding: 2rpx 4rpx;
  border-radius: 4rpx;
}

/* 色盲类型说明部分 */
.info-section {
  margin-bottom: 16rpx; /* 减少底部边距 */
  padding-bottom: 20rpx; /* 增加底部内边距 */
  padding-top: 4rpx; /* 增加顶部内边距 */
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx; /* 添加间距 */
}

.info-intro {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
  padding: 0 6rpx;
  text-align: justify;
  border-left: 3rpx solid rgba(7, 193, 96, 0.3); /* 添加左侧边框 */
  padding-left: 12rpx; /* 增加左侧内边距 */
  background-color: rgba(7, 193, 96, 0.03); /* 添加淡色背景 */
  border-radius: 4rpx; /* 添加圆角 */
  padding-top: 8rpx; /* 增加顶部内边距 */
  padding-bottom: 8rpx; /* 增加底部内边距 */
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr; /* 单列布局 */
  gap: 10rpx; /* 减少间距 */
  margin-top: 6rpx; /* 添加顶部边距 */
}

.info-block {
  padding: 14rpx 16rpx; /* 增加内边距 */
  background-color: #ffffff; /* 使用白色背景 */
  border-radius: 10rpx;
  font-size: 22rpx; /* 减小字体 */
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.06); /* 增强阴影 */
  border: 1rpx solid rgba(0, 0, 0, 0.03); /* 添加极细边框 */
  transition: all 0.2s ease; /* 添加过渡效果 */
  position: relative;
  overflow: hidden;
  display: flex; /* 使用弹性布局 */
  flex-direction: column; /* 垂直排列 */
  margin-bottom: 2rpx; /* 添加底部边距 */
}

.info-block:active {
  transform: scale(0.98); /* 点击时轻微缩小 */
  background-color: #fafafa;
}

/* 添加微妙的背景装饰 */
.info-block::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(7, 193, 96, 0.05); /* 使用主题色的淡色 */
  border-radius: 0 0 0 40rpx;
  z-index: 0;
}

.info-block-title {
  font-size: 26rpx; /* 增大字体 */
  font-weight: bold;
  color: #333; /* 使用深色 */
  margin-bottom: 8rpx; /* 增加底部边距 */
  display: flex;
  align-items: center;
  line-height: 1.4;
}

/* 颜色点样式 */
.color-dot {
  width: 14rpx;
  height: 14rpx;
  border-radius: 50%;
  margin-right: 10rpx;
  display: inline-block;
  flex-shrink: 0;
  box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.red-dot {
  background-color: #e74c3c; /* 红色 */
}

.green-dot {
  background-color: #2ecc71; /* 绿色 */
}

.blue-dot {
  background-color: #3498db; /* 蓝色 */
}

.gray-dot {
  background: linear-gradient(45deg, #333 0%, #999 100%); /* 灰度渐变 */
}

.info-block-text {
  font-size: 24rpx; /* 增大字体 */
  color: #666;
  line-height: 1.6; /* 增加行高 */
  text-align: justify; /* 两端对齐 */
  padding-left: 24rpx; /* 添加左侧内边距，与标题对齐 */
}

/* 颜色选择器弹窗 */
.color-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.color-picker-container {
  width: 90%;
  max-width: 650rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.color-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #e0e0e0;
  background-color: #f8f8f8;
}

.color-picker-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.color-picker-close {
  font-size: 36rpx;
  color: #999;
  line-height: 1;
  padding: 10rpx;
  margin: -10rpx;
}
